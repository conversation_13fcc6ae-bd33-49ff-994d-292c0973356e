@font-face {
 font-family: "boxicons";
 src: url("./boxicons.ttf?********************************") format("truetype"),
url("./boxicons.woff?********************************") format("woff"),
url("./boxicons.woff2?********************************") format("woff2");
 font-weight:normal;
}


[class^="bx"],
[class*=" bx"],
.bx {
 font-family: boxicons !important;
 font-style: normal;
 font-weight: normal;
 font-variant: normal;
 text-transform: none;
 line-height: 1;
 display:inline-block;
 speak:none;
 -webkit-font-smoothing: antialiased;
 -moz-osx-font-smoothing: grayscale;
}

.variable-selector-00:before {
    content: "\fb1e";
}
.bx-8-ball:before {
    content: "\f101";
}
.bx-a-arrow-down:before {
    content: "\f102";
}
.bx-a-arrow-up:before {
    content: "\f103";
}
.bx-accessibility:before {
    content: "\f104";
}
.bx-acorn:before {
    content: "\f105";
}
.bx-address-book:before {
    content: "\f106";
}
.bx-air-conditioner:before {
    content: "\f107";
}
.bx-air:before {
    content: "\f108";
}
.bx-airplay:before {
    content: "\f109";
}
.bx-alarm-alt:before {
    content: "\f10a";
}
.bx-alarm-check:before {
    content: "\f10b";
}
.bx-alarm-exclamation:before {
    content: "\f10c";
}
.bx-alarm-minus:before {
    content: "\f10d";
}
.bx-alarm-plus:before {
    content: "\f10e";
}
.bx-alarm-slash:before {
    content: "\f10f";
}
.bx-alarm-z:before {
    content: "\f110";
}
.bx-alarm:before {
    content: "\f111";
}
.bx-album-covers:before {
    content: "\f112";
}
.bx-alert-circle:before {
    content: "\f113";
}
.bx-alert-octagon:before {
    content: "\f114";
}
.bx-alert-shield:before {
    content: "\f115";
}
.bx-alert-square:before {
    content: "\f116";
}
.bx-alert-triangle:before {
    content: "\f117";
}
.bx-alien:before {
    content: "\f118";
}
.bx-align-center:before {
    content: "\f119";
}
.bx-align-justify:before {
    content: "\f11a";
}
.bx-align-left:before {
    content: "\f11b";
}
.bx-align-right:before {
    content: "\f11c";
}
.bx-ambulance:before {
    content: "\f11d";
}
.bx-ampersand:before {
    content: "\f11e";
}
.bx-analyze:before {
    content: "\f11f";
}
.bx-anchor:before {
    content: "\f120";
}
.bx-angle:before {
    content: "\f121";
}
.bx-angry:before {
    content: "\f122";
}
.bx-animation-bounce:before {
    content: "\f123";
}
.bx-apartment:before {
    content: "\f124";
}
.bx-approximate:before {
    content: "\f125";
}
.bx-apps-alt:before {
    content: "\f126";
}
.bx-apps:before {
    content: "\f127";
}
.bx-arch:before {
    content: "\f128";
}
.bx-archive-alt:before {
    content: "\f129";
}
.bx-archive-arrow-down:before {
    content: "\f12a";
}
.bx-archive-arrow-up:before {
    content: "\f12b";
}
.bx-archive:before {
    content: "\f12c";
}
.bx-area:before {
    content: "\f12d";
}
.bx-arrow-big-down-line:before {
    content: "\f12e";
}
.bx-arrow-big-down:before {
    content: "\f12f";
}
.bx-arrow-big-left-line:before {
    content: "\f130";
}
.bx-arrow-big-left:before {
    content: "\f131";
}
.bx-arrow-big-right-line:before {
    content: "\f132";
}
.bx-arrow-big-right:before {
    content: "\f133";
}
.bx-arrow-big-up-line:before {
    content: "\f134";
}
.bx-arrow-big-up:before {
    content: "\f135";
}
.bx-arrow-down-circle:before {
    content: "\f136";
}
.bx-arrow-down-left-circle:before {
    content: "\f137";
}
.bx-arrow-down-left-square:before {
    content: "\f138";
}
.bx-arrow-down-left-stroke-circle:before {
    content: "\f139";
}
.bx-arrow-down-left-stroke-square:before {
    content: "\f13a";
}
.bx-arrow-down-left-stroke:before {
    content: "\f13b";
}
.bx-arrow-down-left:before {
    content: "\f13c";
}
.bx-arrow-down-right-circle:before {
    content: "\f13d";
}
.bx-arrow-down-right-square:before {
    content: "\f13e";
}
.bx-arrow-down-right-stroke-circle:before {
    content: "\f13f";
}
.bx-arrow-down-right-stroke-square:before {
    content: "\f140";
}
.bx-arrow-down-right-stroke:before {
    content: "\f141";
}
.bx-arrow-down-right:before {
    content: "\f142";
}
.bx-arrow-down-square:before {
    content: "\f143";
}
.bx-arrow-down-stroke-circle:before {
    content: "\f144";
}
.bx-arrow-down-stroke-square:before {
    content: "\f145";
}
.bx-arrow-down-stroke:before {
    content: "\f146";
}
.bx-arrow-down:before {
    content: "\f147";
}
.bx-arrow-from-bottom-stroke:before {
    content: "\f148";
}
.bx-arrow-from-left-stroke:before {
    content: "\f149";
}
.bx-arrow-from-right-stroke:before {
    content: "\f14a";
}
.bx-arrow-from-top-stroke:before {
    content: "\f14b";
}
.bx-arrow-in-down-left-circle:before {
    content: "\f14c";
}
.bx-arrow-in-down-left-square:before {
    content: "\f14d";
}
.bx-arrow-in-down-left-stroke-circle:before {
    content: "\f14e";
}
.bx-arrow-in-down-left-stroke-square:before {
    content: "\f14f";
}
.bx-arrow-in-down-right-circle:before {
    content: "\f150";
}
.bx-arrow-in-down-right-square:before {
    content: "\f151";
}
.bx-arrow-in-down-right-stroke-circle:before {
    content: "\f152";
}
.bx-arrow-in-down-right-stroke-square:before {
    content: "\f153";
}
.bx-arrow-in-down-square-half:before {
    content: "\f154";
}
.bx-arrow-in-left-square-half:before {
    content: "\f155";
}
.bx-arrow-in-right-square-half:before {
    content: "\f156";
}
.bx-arrow-in-up-left-circle:before {
    content: "\f157";
}
.bx-arrow-in-up-left-square:before {
    content: "\f158";
}
.bx-arrow-in-up-left-stroke-circle:before {
    content: "\f159";
}
.bx-arrow-in-up-left-stroke-square:before {
    content: "\f15a";
}
.bx-arrow-in-up-right-circle:before {
    content: "\f15b";
}
.bx-arrow-in-up-right-square:before {
    content: "\f15c";
}
.bx-arrow-in-up-right-stroke-circle:before {
    content: "\f15d";
}
.bx-arrow-in-up-right-stroke-square:before {
    content: "\f15e";
}
.bx-arrow-in-up-square-half:before {
    content: "\f15f";
}
.bx-arrow-left-circle:before {
    content: "\f160";
}
.bx-arrow-left-square:before {
    content: "\f161";
}
.bx-arrow-left-stroke-circle:before {
    content: "\f162";
}
.bx-arrow-left-stroke-square:before {
    content: "\f163";
}
.bx-arrow-left-stroke:before {
    content: "\f164";
}
.bx-arrow-left:before {
    content: "\f165";
}
.bx-arrow-out-down-left-circle:before {
    content: "\f166";
}
.bx-arrow-out-down-left-square:before {
    content: "\f167";
}
.bx-arrow-out-down-left-stroke-circle:before {
    content: "\f168";
}
.bx-arrow-out-down-left-stroke-square:before {
    content: "\f169";
}
.bx-arrow-out-down-right-circle:before {
    content: "\f16a";
}
.bx-arrow-out-down-right-square:before {
    content: "\f16b";
}
.bx-arrow-out-down-right-stroke-circle:before {
    content: "\f16c";
}
.bx-arrow-out-down-right-stroke-square:before {
    content: "\f16d";
}
.bx-arrow-out-down-square-half:before {
    content: "\f16e";
}
.bx-arrow-out-left-square-half:before {
    content: "\f16f";
}
.bx-arrow-out-right-square-half:before {
    content: "\f170";
}
.bx-arrow-out-up-left-circle:before {
    content: "\f171";
}
.bx-arrow-out-up-left-square:before {
    content: "\f172";
}
.bx-arrow-out-up-left-stroke-circle:before {
    content: "\f173";
}
.bx-arrow-out-up-left-stroke-square:before {
    content: "\f174";
}
.bx-arrow-out-up-right-circle:before {
    content: "\f175";
}
.bx-arrow-out-up-right-square:before {
    content: "\f176";
}
.bx-arrow-out-up-right-stroke-circle:before {
    content: "\f177";
}
.bx-arrow-out-up-right-stroke-square:before {
    content: "\f178";
}
.bx-arrow-out-up-square-half:before {
    content: "\f179";
}
.bx-arrow-right-circle:before {
    content: "\f17a";
}
.bx-arrow-right-square:before {
    content: "\f17b";
}
.bx-arrow-right-stroke-circle:before {
    content: "\f17c";
}
.bx-arrow-right-stroke-square:before {
    content: "\f17d";
}
.bx-arrow-right-stroke:before {
    content: "\f17e";
}
.bx-arrow-right:before {
    content: "\f17f";
}
.bx-arrow-s-down:before {
    content: "\f180";
}
.bx-arrow-s-left:before {
    content: "\f181";
}
.bx-arrow-s-right:before {
    content: "\f182";
}
.bx-arrow-s-up:before {
    content: "\f183";
}
.bx-arrow-to-bottom-stroke:before {
    content: "\f184";
}
.bx-arrow-to-left-stroke:before {
    content: "\f185";
}
.bx-arrow-to-right-stroke:before {
    content: "\f186";
}
.bx-arrow-to-top-stroke:before {
    content: "\f187";
}
.bx-arrow-up-circle:before {
    content: "\f188";
}
.bx-arrow-up-left-circle:before {
    content: "\f189";
}
.bx-arrow-up-left-square:before {
    content: "\f18a";
}
.bx-arrow-up-left-stroke-circle:before {
    content: "\f18b";
}
.bx-arrow-up-left-stroke-square:before {
    content: "\f18c";
}
.bx-arrow-up-left-stroke:before {
    content: "\f18d";
}
.bx-arrow-up-left:before {
    content: "\f18e";
}
.bx-arrow-up-right-circle:before {
    content: "\f18f";
}
.bx-arrow-up-right-square:before {
    content: "\f190";
}
.bx-arrow-up-right-stroke-circle:before {
    content: "\f191";
}
.bx-arrow-up-right-stroke-square:before {
    content: "\f192";
}
.bx-arrow-up-right-stroke:before {
    content: "\f193";
}
.bx-arrow-up-right:before {
    content: "\f194";
}
.bx-arrow-up-square:before {
    content: "\f195";
}
.bx-arrow-up-stroke-circle:before {
    content: "\f196";
}
.bx-arrow-up-stroke-square:before {
    content: "\f197";
}
.bx-arrow-up-stroke:before {
    content: "\f198";
}
.bx-arrow-up:before {
    content: "\f199";
}
.bx-article:before {
    content: "\f19a";
}
.bx-asterisk:before {
    content: "\f19b";
}
.bx-at:before {
    content: "\f19c";
}
.bx-avocado:before {
    content: "\f19d";
}
.bx-axe:before {
    content: "\f19e";
}
.bx-background-color-fill:before {
    content: "\f19f";
}
.bx-background:before {
    content: "\f1a0";
}
.bx-backpack-star:before {
    content: "\f1a1";
}
.bx-backpack:before {
    content: "\f1a2";
}
.bx-backspace:before {
    content: "\f1a3";
}
.bx-badge-check:before {
    content: "\f1a4";
}
.bx-badge-exclamation:before {
    content: "\f1a5";
}
.bx-badge-info:before {
    content: "\f1a6";
}
.bx-badge:before {
    content: "\f1a7";
}
.bx-baguette:before {
    content: "\f1a8";
}
.bx-balcony:before {
    content: "\f1a9";
}
.bx-ball-throw:before {
    content: "\f1aa";
}
.bx-balloon:before {
    content: "\f1ab";
}
.bx-band-aid:before {
    content: "\f1ac";
}
.bx-bank:before {
    content: "\f1ad";
}
.bx-bar-chart-big:before {
    content: "\f1ae";
}
.bx-bar-chart-square:before {
    content: "\f1af";
}
.bx-bar-chart:before {
    content: "\f1b0";
}
.bx-barcode-square:before {
    content: "\f1b1";
}
.bx-barcode:before {
    content: "\f1b2";
}
.bx-barn:before {
    content: "\f1b3";
}
.bx-baseball:before {
    content: "\f1b4";
}
.bx-basket:before {
    content: "\f1b5";
}
.bx-basketball:before {
    content: "\f1b6";
}
.bx-bath:before {
    content: "\f1b7";
}
.bx-battery-1:before {
    content: "\f1b8";
}
.bx-battery-2:before {
    content: "\f1b9";
}
.bx-battery-3:before {
    content: "\f1ba";
}
.bx-battery-full:before {
    content: "\f1bb";
}
.bx-battery-low:before {
    content: "\f1bc";
}
.bx-battery:before {
    content: "\f1bd";
}
.bx-beach-ball:before {
    content: "\f1be";
}
.bx-beach:before {
    content: "\f1bf";
}
.bx-beaker:before {
    content: "\f1c0";
}
.bx-beanie:before {
    content: "\f1c1";
}
.bx-bear:before {
    content: "\f1c2";
}
.bx-bed-alt:before {
    content: "\f1c3";
}
.bx-bed:before {
    content: "\f1c4";
}
.bx-beer:before {
    content: "\f1c5";
}
.bx-bell-check:before {
    content: "\f1c6";
}
.bx-bell-minus:before {
    content: "\f1c7";
}
.bx-bell-plus:before {
    content: "\f1c8";
}
.bx-bell-ring:before {
    content: "\f1c9";
}
.bx-bell-slash:before {
    content: "\f1ca";
}
.bx-bell:before {
    content: "\f1cb";
}
.bx-bench:before {
    content: "\f1cc";
}
.bx-bible:before {
    content: "\f1cd";
}
.bx-binocular:before {
    content: "\f1ce";
}
.bx-bird-alt:before {
    content: "\f1cf";
}
.bx-bird:before {
    content: "\f1d0";
}
.bx-birthday-cake:before {
    content: "\f1d1";
}
.bx-bitcoin:before {
    content: "\f1d2";
}
.bx-blanket:before {
    content: "\f1d3";
}
.bx-block:before {
    content: "\f1d4";
}
.bx-blockquote:before {
    content: "\f1d5";
}
.bx-blocks:before {
    content: "\f1d6";
}
.bx-bluetooth:before {
    content: "\f1d7";
}
.bx-blur:before {
    content: "\f1d8";
}
.bx-body:before {
    content: "\f1d9";
}
.bx-bold:before {
    content: "\f1da";
}
.bx-bolt-circle:before {
    content: "\f1db";
}
.bx-bolt-square:before {
    content: "\f1dc";
}
.bx-bolt:before {
    content: "\f1dd";
}
.bx-bomb:before {
    content: "\f1de";
}
.bx-bone:before {
    content: "\f1df";
}
.bx-bong:before {
    content: "\f1e0";
}
.bx-book-add:before {
    content: "\f1e1";
}
.bx-book-alt:before {
    content: "\f1e2";
}
.bx-book-bookmark:before {
    content: "\f1e3";
}
.bx-book-content:before {
    content: "\f1e4";
}
.bx-book-heart:before {
    content: "\f1e5";
}
.bx-book-library:before {
    content: "\f1e6";
}
.bx-book-open:before {
    content: "\f1e7";
}
.bx-book:before {
    content: "\f1e8";
}
.bx-bookmark-alt:before {
    content: "\f1e9";
}
.bx-bookmark-heart:before {
    content: "\f1ea";
}
.bx-bookmark-minus-alt:before {
    content: "\f1eb";
}
.bx-bookmark-minus:before {
    content: "\f1ec";
}
.bx-bookmark-plus-alt:before {
    content: "\f1ed";
}
.bx-bookmark-plus:before {
    content: "\f1ee";
}
.bx-bookmark-star:before {
    content: "\f1ef";
}
.bx-bookmark:before {
    content: "\f1f0";
}
.bx-bookmarks:before {
    content: "\f1f1";
}
.bx-boombox:before {
    content: "\f1f2";
}
.bx-boot:before {
    content: "\f1f3";
}
.bx-border-all:before {
    content: "\f1f4";
}
.bx-border-bottom:before {
    content: "\f1f5";
}
.bx-border-inner:before {
    content: "\f1f6";
}
.bx-border-left:before {
    content: "\f1f7";
}
.bx-border-none:before {
    content: "\f1f8";
}
.bx-border-outer:before {
    content: "\f1f9";
}
.bx-border-radius:before {
    content: "\f1fa";
}
.bx-border-right:before {
    content: "\f1fb";
}
.bx-border-top:before {
    content: "\f1fc";
}
.bx-bow:before {
    content: "\f1fd";
}
.bx-bowl-balls:before {
    content: "\f1fe";
}
.bx-bowl-bubbles:before {
    content: "\f1ff";
}
.bx-bowl-hot:before {
    content: "\f200";
}
.bx-bowl-noodles-alt:before {
    content: "\f201";
}
.bx-bowl-noodles:before {
    content: "\f202";
}
.bx-bowl-rice:before {
    content: "\f203";
}
.bx-bowling-ball:before {
    content: "\f204";
}
.bx-box-alt:before {
    content: "\f205";
}
.bx-box:before {
    content: "\f206";
}
.bx-bracket-curly:before {
    content: "\f207";
}
.bx-bracket-round:before {
    content: "\f208";
}
.bx-bracket:before {
    content: "\f209";
}
.bx-braille:before {
    content: "\f20a";
}
.bx-brain:before {
    content: "\f20b";
}
.bx-bread:before {
    content: "\f20c";
}
.bx-brick:before {
    content: "\f20d";
}
.bx-bridge:before {
    content: "\f20e";
}
.bx-briefcase-alt-2:before {
    content: "\f20f";
}
.bx-briefcase-alt:before {
    content: "\f210";
}
.bx-briefcase:before {
    content: "\f211";
}
.bx-brightness-half:before {
    content: "\f212";
}
.bx-brightness:before {
    content: "\f213";
}
.bx-broadcast:before {
    content: "\f214";
}
.bx-browser-activity:before {
    content: "\f215";
}
.bx-brush:before {
    content: "\f216";
}
.bx-bug-alt:before {
    content: "\f217";
}
.bx-bug:before {
    content: "\f218";
}
.bx-building-house:before {
    content: "\f219";
}
.bx-building:before {
    content: "\f21a";
}
.bx-buildings:before {
    content: "\f21b";
}
.bx-bullseye:before {
    content: "\f21c";
}
.bx-buoy:before {
    content: "\f21d";
}
.bx-burger-alt:before {
    content: "\f21e";
}
.bx-burger:before {
    content: "\f21f";
}
.bx-bus:before {
    content: "\f220";
}
.bx-business:before {
    content: "\f221";
}
.bx-button-rounded:before {
    content: "\f222";
}
.bx-button:before {
    content: "\f223";
}
.bx-cabinet:before {
    content: "\f224";
}
.bx-cable-car:before {
    content: "\f225";
}
.bx-cake-slice:before {
    content: "\f226";
}
.bx-calculator:before {
    content: "\f227";
}
.bx-calendar-alt-2:before {
    content: "\f228";
}
.bx-calendar-alt:before {
    content: "\f229";
}
.bx-calendar-check:before {
    content: "\f22a";
}
.bx-calendar-cog:before {
    content: "\f22b";
}
.bx-calendar-detail:before {
    content: "\f22c";
}
.bx-calendar-down-arrow:before {
    content: "\f22d";
}
.bx-calendar-event:before {
    content: "\f22e";
}
.bx-calendar-heart:before {
    content: "\f22f";
}
.bx-calendar-minus:before {
    content: "\f230";
}
.bx-calendar-plus:before {
    content: "\f231";
}
.bx-calendar-search:before {
    content: "\f232";
}
.bx-calendar-star:before {
    content: "\f233";
}
.bx-calendar-up-arrow:before {
    content: "\f234";
}
.bx-calendar-week:before {
    content: "\f235";
}
.bx-calendar-x:before {
    content: "\f236";
}
.bx-calendar:before {
    content: "\f237";
}
.bx-camcoder:before {
    content: "\f238";
}
.bx-camera-alt:before {
    content: "\f239";
}
.bx-camera-flip:before {
    content: "\f23a";
}
.bx-camera-home:before {
    content: "\f23b";
}
.bx-camera-monochrome:before {
    content: "\f23c";
}
.bx-camera-plus:before {
    content: "\f23d";
}
.bx-camera-portrait:before {
    content: "\f23e";
}
.bx-camera-slash:before {
    content: "\f23f";
}
.bx-camera-switch:before {
    content: "\f240";
}
.bx-camera:before {
    content: "\f241";
}
.bx-campfire:before {
    content: "\f242";
}
.bx-camping:before {
    content: "\f243";
}
.bx-candlestick:before {
    content: "\f244";
}
.bx-cannabis:before {
    content: "\f245";
}
.bx-cap:before {
    content: "\f246";
}
.bx-capitalize:before {
    content: "\f247";
}
.bx-capsule:before {
    content: "\f248";
}
.bx-captions-cc:before {
    content: "\f249";
}
.bx-captions:before {
    content: "\f24a";
}
.bx-capture:before {
    content: "\f24b";
}
.bx-car-battery:before {
    content: "\f24c";
}
.bx-car-key:before {
    content: "\f24d";
}
.bx-car:before {
    content: "\f24e";
}
.bx-card-view-large:before {
    content: "\f24f";
}
.bx-card-view-no-title:before {
    content: "\f250";
}
.bx-card-view-small:before {
    content: "\f251";
}
.bx-card-view-tiles:before {
    content: "\f252";
}
.bx-card-view:before {
    content: "\f253";
}
.bx-caret-down-circle:before {
    content: "\f254";
}
.bx-caret-down-square:before {
    content: "\f255";
}
.bx-caret-down:before {
    content: "\f256";
}
.bx-caret-left-circle:before {
    content: "\f257";
}
.bx-caret-left-square:before {
    content: "\f258";
}
.bx-caret-left:before {
    content: "\f259";
}
.bx-caret-right-circle:before {
    content: "\f25a";
}
.bx-caret-right-square:before {
    content: "\f25b";
}
.bx-caret-right:before {
    content: "\f25c";
}
.bx-caret-up-circle:before {
    content: "\f25d";
}
.bx-caret-up-square:before {
    content: "\f25e";
}
.bx-caret-up:before {
    content: "\f25f";
}
.bx-carets-down-up:before {
    content: "\f260";
}
.bx-carets-left-right:before {
    content: "\f261";
}
.bx-carets-right-left:before {
    content: "\f262";
}
.bx-carets-up-down:before {
    content: "\f263";
}
.bx-carrot:before {
    content: "\f264";
}
.bx-cart-minus:before {
    content: "\f265";
}
.bx-cart-plus:before {
    content: "\f266";
}
.bx-cart:before {
    content: "\f267";
}
.bx-cast:before {
    content: "\f268";
}
.bx-castle:before {
    content: "\f269";
}
.bx-cat:before {
    content: "\f26a";
}
.bx-categories:before {
    content: "\f26b";
}
.bx-cctv:before {
    content: "\f26c";
}
.bx-certification:before {
    content: "\f26d";
}
.bx-chair:before {
    content: "\f26e";
}
.bx-champagne:before {
    content: "\f26f";
}
.bx-chart-area:before {
    content: "\f270";
}
.bx-chart-bar-big-columns:before {
    content: "\f271";
}
.bx-chart-bar-big-rows:before {
    content: "\f272";
}
.bx-chart-bar-columns:before {
    content: "\f273";
}
.bx-chart-bar-rows:before {
    content: "\f274";
}
.bx-chart-bubble:before {
    content: "\f275";
}
.bx-chart-gantt:before {
    content: "\f276";
}
.bx-chart-line:before {
    content: "\f277";
}
.bx-chart-network:before {
    content: "\f278";
}
.bx-chart-scatter:before {
    content: "\f279";
}
.bx-chart-spline:before {
    content: "\f27a";
}
.bx-chart-stacked-columns:before {
    content: "\f27b";
}
.bx-chart-stacked-rows:before {
    content: "\f27c";
}
.bx-chart-trend:before {
    content: "\f27d";
}
.bx-check-circle:before {
    content: "\f27e";
}
.bx-check-square:before {
    content: "\f27f";
}
.bx-check:before {
    content: "\f280";
}
.bx-checkbox-checked:before {
    content: "\f281";
}
.bx-checkbox-square:before {
    content: "\f282";
}
.bx-checkbox:before {
    content: "\f283";
}
.bx-checklist:before {
    content: "\f284";
}
.bx-checks:before {
    content: "\f285";
}
.bx-cheese:before {
    content: "\f286";
}
.bx-chef-hat:before {
    content: "\f287";
}
.bx-cherry:before {
    content: "\f288";
}
.bx-chess-bishop:before {
    content: "\f289";
}
.bx-chess-king:before {
    content: "\f28a";
}
.bx-chess-knight:before {
    content: "\f28b";
}
.bx-chess-pawn:before {
    content: "\f28c";
}
.bx-chess-queen:before {
    content: "\f28d";
}
.bx-chess-rook:before {
    content: "\f28e";
}
.bx-chess:before {
    content: "\f28f";
}
.bx-chevron-down-circle:before {
    content: "\f290";
}
.bx-chevron-down-square:before {
    content: "\f291";
}
.bx-chevron-down:before {
    content: "\f292";
}
.bx-chevron-left-circle:before {
    content: "\f293";
}
.bx-chevron-left-square:before {
    content: "\f294";
}
.bx-chevron-left:before {
    content: "\f295";
}
.bx-chevron-right-circle:before {
    content: "\f296";
}
.bx-chevron-right-square:before {
    content: "\f297";
}
.bx-chevron-right:before {
    content: "\f298";
}
.bx-chevron-up-circle:before {
    content: "\f299";
}
.bx-chevron-up-square:before {
    content: "\f29a";
}
.bx-chevron-up:before {
    content: "\f29b";
}
.bx-chevrons-down-up:before {
    content: "\f29c";
}
.bx-chevrons-down:before {
    content: "\f29d";
}
.bx-chevrons-left-right:before {
    content: "\f29e";
}
.bx-chevrons-left:before {
    content: "\f29f";
}
.bx-chevrons-right-left:before {
    content: "\f2a0";
}
.bx-chevrons-right:before {
    content: "\f2a1";
}
.bx-chevrons-up-down:before {
    content: "\f2a2";
}
.bx-chevrons-up:before {
    content: "\f2a3";
}
.bx-child:before {
    content: "\f2a4";
}
.bx-chip:before {
    content: "\f2a5";
}
.bx-church:before {
    content: "\f2a6";
}
.bx-cigarette:before {
    content: "\f2a7";
}
.bx-circle-dashed-half:before {
    content: "\f2a8";
}
.bx-circle-dashed:before {
    content: "\f2a9";
}
.bx-circle-half-alt:before {
    content: "\f2aa";
}
.bx-circle-half:before {
    content: "\f2ab";
}
.bx-circle-hexagon:before {
    content: "\f2ac";
}
.bx-circle-outer-dashed-circle:before {
    content: "\f2ad";
}
.bx-circle-quarter-alt:before {
    content: "\f2ae";
}
.bx-circle-quarter:before {
    content: "\f2af";
}
.bx-circle-three-quarter-alt:before {
    content: "\f2b0";
}
.bx-circle-three-quarter:before {
    content: "\f2b1";
}
.bx-circle:before {
    content: "\f2b2";
}
.bx-city:before {
    content: "\f2b3";
}
.bx-clipboard-check:before {
    content: "\f2b4";
}
.bx-clipboard-code:before {
    content: "\f2b5";
}
.bx-clipboard-detail:before {
    content: "\f2b6";
}
.bx-clipboard-minus:before {
    content: "\f2b7";
}
.bx-clipboard-plus:before {
    content: "\f2b8";
}
.bx-clipboard-x:before {
    content: "\f2b9";
}
.bx-clipboard:before {
    content: "\f2ba";
}
.bx-clock-1:before {
    content: "\f2bb";
}
.bx-clock-10:before {
    content: "\f2bc";
}
.bx-clock-11:before {
    content: "\f2bd";
}
.bx-clock-12:before {
    content: "\f2be";
}
.bx-clock-2:before {
    content: "\f2bf";
}
.bx-clock-3:before {
    content: "\f2c0";
}
.bx-clock-4:before {
    content: "\f2c1";
}
.bx-clock-5:before {
    content: "\f2c2";
}
.bx-clock-6:before {
    content: "\f2c3";
}
.bx-clock-7:before {
    content: "\f2c4";
}
.bx-clock-8:before {
    content: "\f2c5";
}
.bx-clock-9:before {
    content: "\f2c6";
}
.bx-cloud-alt:before {
    content: "\f2c7";
}
.bx-cloud-drizzle:before {
    content: "\f2c8";
}
.bx-cloud-fog:before {
    content: "\f2c9";
}
.bx-cloud-lightning:before {
    content: "\f2ca";
}
.bx-cloud-moon:before {
    content: "\f2cb";
}
.bx-cloud-rain-wind:before {
    content: "\f2cc";
}
.bx-cloud-rain:before {
    content: "\f2cd";
}
.bx-cloud-snow:before {
    content: "\f2ce";
}
.bx-cloud-sun:before {
    content: "\f2cf";
}
.bx-cloud:before {
    content: "\f2d0";
}
.bx-club:before {
    content: "\f2d1";
}
.bx-cocktail:before {
    content: "\f2d2";
}
.bx-code-alt:before {
    content: "\f2d3";
}
.bx-code:before {
    content: "\f2d4";
}
.bx-coffee-beans:before {
    content: "\f2d5";
}
.bx-coffee-cup:before {
    content: "\f2d6";
}
.bx-coffee:before {
    content: "\f2d7";
}
.bx-cog:before {
    content: "\f2d8";
}
.bx-cognition:before {
    content: "\f2d9";
}
.bx-coin:before {
    content: "\f2da";
}
.bx-coins:before {
    content: "\f2db";
}
.bx-col-resize:before {
    content: "\f2dc";
}
.bx-color-fill:before {
    content: "\f2dd";
}
.bx-color-wheel:before {
    content: "\f2de";
}
.bx-columns-3:before {
    content: "\f2df";
}
.bx-columns-4:before {
    content: "\f2e0";
}
.bx-columns:before {
    content: "\f2e1";
}
.bx-command:before {
    content: "\f2e2";
}
.bx-community:before {
    content: "\f2e3";
}
.bx-compare-alt:before {
    content: "\f2e4";
}
.bx-compare:before {
    content: "\f2e5";
}
.bx-compass:before {
    content: "\f2e6";
}
.bx-component:before {
    content: "\f2e7";
}
.bx-computer:before {
    content: "\f2e8";
}
.bx-confused:before {
    content: "\f2e9";
}
.bx-connector:before {
    content: "\f2ea";
}
.bx-contact-book:before {
    content: "\f2eb";
}
.bx-contrast:before {
    content: "\f2ec";
}
.bx-cookie:before {
    content: "\f2ed";
}
.bx-cool:before {
    content: "\f2ee";
}
.bx-copy-check:before {
    content: "\f2ef";
}
.bx-copy-list:before {
    content: "\f2f0";
}
.bx-copy-minus:before {
    content: "\f2f1";
}
.bx-copy-plus:before {
    content: "\f2f2";
}
.bx-copy-x:before {
    content: "\f2f3";
}
.bx-copy:before {
    content: "\f2f4";
}
.bx-copyright:before {
    content: "\f2f5";
}
.bx-credit-card-alt:before {
    content: "\f2f6";
}
.bx-credit-card-front:before {
    content: "\f2f7";
}
.bx-credit-card-insert:before {
    content: "\f2f8";
}
.bx-credit-card:before {
    content: "\f2f9";
}
.bx-cricket-ball:before {
    content: "\f2fa";
}
.bx-crop:before {
    content: "\f2fb";
}
.bx-crosshair:before {
    content: "\f2fc";
}
.bx-crown:before {
    content: "\f2fd";
}
.bx-cube-alt:before {
    content: "\f2fe";
}
.bx-cube-inside:before {
    content: "\f2ff";
}
.bx-cube:before {
    content: "\f300";
}
.bx-cuboid:before {
    content: "\f301";
}
.bx-cup-hot:before {
    content: "\f302";
}
.bx-cup-saucer:before {
    content: "\f303";
}
.bx-cup-tea:before {
    content: "\f304";
}
.bx-cup:before {
    content: "\f305";
}
.bx-cupboard-alt:before {
    content: "\f306";
}
.bx-cupboard:before {
    content: "\f307";
}
.bx-cupcake:before {
    content: "\f308";
}
.bx-currency-note:before {
    content: "\f309";
}
.bx-currency-notes:before {
    content: "\f30a";
}
.bx-cursor-add:before {
    content: "\f30b";
}
.bx-cursor-cell:before {
    content: "\f30c";
}
.bx-cursor-crosshair-dot:before {
    content: "\f30d";
}
.bx-cursor-crosshair:before {
    content: "\f30e";
}
.bx-cursor-pen:before {
    content: "\f30f";
}
.bx-cursor-pointer:before {
    content: "\f310";
}
.bx-cursor:before {
    content: "\f311";
}
.bx-cut:before {
    content: "\f312";
}
.bx-cycling:before {
    content: "\f313";
}
.bx-cylinder:before {
    content: "\f314";
}
.bx-dashboard-alt:before {
    content: "\f315";
}
.bx-dashboard:before {
    content: "\f316";
}
.bx-database-alt:before {
    content: "\f317";
}
.bx-database:before {
    content: "\f318";
}
.bx-decrease-indent:before {
    content: "\f319";
}
.bx-department-store:before {
    content: "\f31a";
}
.bx-desert:before {
    content: "\f31b";
}
.bx-desk:before {
    content: "\f31c";
}
.bx-desktop-alt:before {
    content: "\f31d";
}
.bx-desktop:before {
    content: "\f31e";
}
.bx-devices:before {
    content: "\f31f";
}
.bx-dialpad:before {
    content: "\f320";
}
.bx-diameter:before {
    content: "\f321";
}
.bx-diamond-alt:before {
    content: "\f322";
}
.bx-diamond:before {
    content: "\f323";
}
.bx-diamonds:before {
    content: "\f324";
}
.bx-dice-1:before {
    content: "\f325";
}
.bx-dice-2:before {
    content: "\f326";
}
.bx-dice-3:before {
    content: "\f327";
}
.bx-dice-4:before {
    content: "\f328";
}
.bx-dice-5:before {
    content: "\f329";
}
.bx-dice-6:before {
    content: "\f32a";
}
.bx-dice-roll:before {
    content: "\f32b";
}
.bx-dino:before {
    content: "\f32c";
}
.bx-directions:before {
    content: "\f32d";
}
.bx-disc:before {
    content: "\f32e";
}
.bx-discount:before {
    content: "\f32f";
}
.bx-discussion:before {
    content: "\f330";
}
.bx-dish:before {
    content: "\f331";
}
.bx-dishwasher:before {
    content: "\f332";
}
.bx-dislike:before {
    content: "\f333";
}
.bx-dizzy:before {
    content: "\f334";
}
.bx-dna:before {
    content: "\f335";
}
.bx-dock-bottom-alt:before {
    content: "\f336";
}
.bx-dock-bottom-arrow:before {
    content: "\f337";
}
.bx-dock-bottom-left-alt:before {
    content: "\f338";
}
.bx-dock-bottom-left:before {
    content: "\f339";
}
.bx-dock-bottom-right-alt:before {
    content: "\f33a";
}
.bx-dock-bottom-right:before {
    content: "\f33b";
}
.bx-dock-bottom:before {
    content: "\f33c";
}
.bx-dock-left-alt:before {
    content: "\f33d";
}
.bx-dock-left-arrow:before {
    content: "\f33e";
}
.bx-dock-left:before {
    content: "\f33f";
}
.bx-dock-right-alt:before {
    content: "\f340";
}
.bx-dock-right-arrow:before {
    content: "\f341";
}
.bx-dock-right:before {
    content: "\f342";
}
.bx-dock-top-alt:before {
    content: "\f343";
}
.bx-dock-top-arrow:before {
    content: "\f344";
}
.bx-dock-top-left-alt:before {
    content: "\f345";
}
.bx-dock-top-left:before {
    content: "\f346";
}
.bx-dock-top-right-alt:before {
    content: "\f347";
}
.bx-dock-top-right:before {
    content: "\f348";
}
.bx-dock-top:before {
    content: "\f349";
}
.bx-dog-alt:before {
    content: "\f34a";
}
.bx-dog:before {
    content: "\f34b";
}
.bx-dollar-circle:before {
    content: "\f34c";
}
.bx-dollar:before {
    content: "\f34d";
}
.bx-donate-blood:before {
    content: "\f34e";
}
.bx-donate-heart:before {
    content: "\f34f";
}
.bx-donut:before {
    content: "\f350";
}
.bx-door-open:before {
    content: "\f351";
}
.bx-door:before {
    content: "\f352";
}
.bx-dots-horizontal-rounded-circle:before {
    content: "\f353";
}
.bx-dots-horizontal-rounded:before {
    content: "\f354";
}
.bx-dots-horizontal:before {
    content: "\f355";
}
.bx-dots-vertical-rounded-circle:before {
    content: "\f356";
}
.bx-dots-vertical-rounded:before {
    content: "\f357";
}
.bx-dots-vertical:before {
    content: "\f358";
}
.bx-doughnut-chart:before {
    content: "\f359";
}
.bx-draw-ahead:before {
    content: "\f35a";
}
.bx-draw-behind:before {
    content: "\f35b";
}
.bx-draw-inside:before {
    content: "\f35c";
}
.bx-dress:before {
    content: "\f35d";
}
.bx-dribbling:before {
    content: "\f35e";
}
.bx-dropdown:before {
    content: "\f35f";
}
.bx-dryer:before {
    content: "\f360";
}
.bx-dumbbell-alt:before {
    content: "\f361";
}
.bx-dumbbell:before {
    content: "\f362";
}
.bx-ear-alt:before {
    content: "\f363";
}
.bx-ear-slash:before {
    content: "\f364";
}
.bx-ear:before {
    content: "\f365";
}
.bx-earbuds:before {
    content: "\f366";
}
.bx-ease-in-out:before {
    content: "\f367";
}
.bx-ease-in:before {
    content: "\f368";
}
.bx-ease-out:before {
    content: "\f369";
}
.bx-edit-alt:before {
    content: "\f36a";
}
.bx-edit:before {
    content: "\f36b";
}
.bx-education:before {
    content: "\f36c";
}
.bx-egg-fried:before {
    content: "\f36d";
}
.bx-egg-yolk:before {
    content: "\f36e";
}
.bx-egg:before {
    content: "\f36f";
}
.bx-eject:before {
    content: "\f370";
}
.bx-enterprise:before {
    content: "\f371";
}
.bx-envelope-alt:before {
    content: "\f372";
}
.bx-envelope-open:before {
    content: "\f373";
}
.bx-envelope:before {
    content: "\f374";
}
.bx-equal-circle:before {
    content: "\f375";
}
.bx-equal-square:before {
    content: "\f376";
}
.bx-equal:before {
    content: "\f377";
}
.bx-equalizer:before {
    content: "\f378";
}
.bx-eraser:before {
    content: "\f379";
}
.bx-euro:before {
    content: "\f37a";
}
.bx-ev-station:before {
    content: "\f37b";
}
.bx-expand-left:before {
    content: "\f37c";
}
.bx-expand-right:before {
    content: "\f37d";
}
.bx-exposure:before {
    content: "\f37e";
}
.bx-extension:before {
    content: "\f37f";
}
.bx-eye-alt:before {
    content: "\f380";
}
.bx-eye-big:before {
    content: "\f381";
}
.bx-eye-closed:before {
    content: "\f382";
}
.bx-eye-slash:before {
    content: "\f383";
}
.bx-eye:before {
    content: "\f384";
}
.bx-eyedropper:before {
    content: "\f385";
}
.bx-face-alt-2:before {
    content: "\f386";
}
.bx-face-alt-3:before {
    content: "\f387";
}
.bx-face-alt-4:before {
    content: "\f388";
}
.bx-face-alt:before {
    content: "\f389";
}
.bx-face-child:before {
    content: "\f38a";
}
.bx-face-mask:before {
    content: "\f38b";
}
.bx-face:before {
    content: "\f38c";
}
.bx-factory:before {
    content: "\f38d";
}
.bx-fast-forward-circle:before {
    content: "\f38e";
}
.bx-fast-forward:before {
    content: "\f38f";
}
.bx-female:before {
    content: "\f390";
}
.bx-file-code:before {
    content: "\f391";
}
.bx-file-cog:before {
    content: "\f392";
}
.bx-file-detail:before {
    content: "\f393";
}
.bx-file-heart:before {
    content: "\f394";
}
.bx-file-minus:before {
    content: "\f395";
}
.bx-file-plus:before {
    content: "\f396";
}
.bx-file-report:before {
    content: "\f397";
}
.bx-file-search:before {
    content: "\f398";
}
.bx-file-star:before {
    content: "\f399";
}
.bx-file-x:before {
    content: "\f39a";
}
.bx-file-zip:before {
    content: "\f39b";
}
.bx-file:before {
    content: "\f39c";
}
.bx-film-roll-alt:before {
    content: "\f39d";
}
.bx-film-roll:before {
    content: "\f39e";
}
.bx-film:before {
    content: "\f39f";
}
.bx-filter:before {
    content: "\f3a0";
}
.bx-finger-down:before {
    content: "\f3a1";
}
.bx-finger-left:before {
    content: "\f3a2";
}
.bx-finger-right:before {
    content: "\f3a3";
}
.bx-finger-swipe-down:before {
    content: "\f3a4";
}
.bx-finger-swipe-left:before {
    content: "\f3a5";
}
.bx-finger-swipe-right:before {
    content: "\f3a6";
}
.bx-finger-swipe-up:before {
    content: "\f3a7";
}
.bx-finger-up:before {
    content: "\f3a8";
}
.bx-fingerprint:before {
    content: "\f3a9";
}
.bx-fire-alt:before {
    content: "\f3aa";
}
.bx-fire-extinguisher:before {
    content: "\f3ab";
}
.bx-fire:before {
    content: "\f3ac";
}
.bx-first:before {
    content: "\f3ad";
}
.bx-fish-alt:before {
    content: "\f3ae";
}
.bx-fish:before {
    content: "\f3af";
}
.bx-flag-alt-2:before {
    content: "\f3b0";
}
.bx-flag-alt-3:before {
    content: "\f3b1";
}
.bx-flag-alt:before {
    content: "\f3b2";
}
.bx-flag-chequered:before {
    content: "\f3b3";
}
.bx-flag:before {
    content: "\f3b4";
}
.bx-flame:before {
    content: "\f3b5";
}
.bx-flask-round:before {
    content: "\f3b6";
}
.bx-florist:before {
    content: "\f3b7";
}
.bx-flower-alt-2:before {
    content: "\f3b8";
}
.bx-flower-alt:before {
    content: "\f3b9";
}
.bx-flower:before {
    content: "\f3ba";
}
.bx-folder-check:before {
    content: "\f3bb";
}
.bx-folder-code:before {
    content: "\f3bc";
}
.bx-folder-cog:before {
    content: "\f3bd";
}
.bx-folder-down-arrow:before {
    content: "\f3be";
}
.bx-folder-heart:before {
    content: "\f3bf";
}
.bx-folder-minus:before {
    content: "\f3c0";
}
.bx-folder-open:before {
    content: "\f3c1";
}
.bx-folder-plus:before {
    content: "\f3c2";
}
.bx-folder-search:before {
    content: "\f3c3";
}
.bx-folder-star:before {
    content: "\f3c4";
}
.bx-folder-up-arrow:before {
    content: "\f3c5";
}
.bx-folder-x:before {
    content: "\f3c6";
}
.bx-folder-zip:before {
    content: "\f3c7";
}
.bx-folder:before {
    content: "\f3c8";
}
.bx-font-color:before {
    content: "\f3c9";
}
.bx-font-family:before {
    content: "\f3ca";
}
.bx-food-menu:before {
    content: "\f3cb";
}
.bx-food-tag:before {
    content: "\f3cc";
}
.bx-football-kick:before {
    content: "\f3cd";
}
.bx-football-pitch:before {
    content: "\f3ce";
}
.bx-football:before {
    content: "\f3cf";
}
.bx-footsteps:before {
    content: "\f3d0";
}
.bx-foreground:before {
    content: "\f3d1";
}
.bx-fork-knife:before {
    content: "\f3d2";
}
.bx-fork-spoon:before {
    content: "\f3d3";
}
.bx-fork:before {
    content: "\f3d4";
}
.bx-form:before {
    content: "\f3d5";
}
.bx-forward-big:before {
    content: "\f3d6";
}
.bx-forward-stroke:before {
    content: "\f3d7";
}
.bx-forward:before {
    content: "\f3d8";
}
.bx-frame:before {
    content: "\f3d9";
}
.bx-fridge:before {
    content: "\f3da";
}
.bx-fullscreen-exit:before {
    content: "\f3db";
}
.bx-fullscreen:before {
    content: "\f3dc";
}
.bx-functions:before {
    content: "\f3dd";
}
.bx-future:before {
    content: "\f3de";
}
.bx-gallery-horizontal-end:before {
    content: "\f3df";
}
.bx-gallery-horizontal:before {
    content: "\f3e0";
}
.bx-gallery-thumbnails:before {
    content: "\f3e1";
}
.bx-gallery-vertical-end:before {
    content: "\f3e2";
}
.bx-gallery-vertical:before {
    content: "\f3e3";
}
.bx-gaming:before {
    content: "\f3e4";
}
.bx-garage:before {
    content: "\f3e5";
}
.bx-gavel:before {
    content: "\f3e6";
}
.bx-gear:before {
    content: "\f3e7";
}
.bx-gem:before {
    content: "\f3e8";
}
.bx-gestures:before {
    content: "\f3e9";
}
.bx-ghost:before {
    content: "\f3ea";
}
.bx-gift:before {
    content: "\f3eb";
}
.bx-git-branch:before {
    content: "\f3ec";
}
.bx-git-commit:before {
    content: "\f3ed";
}
.bx-git-compare:before {
    content: "\f3ee";
}
.bx-git-merge-queue:before {
    content: "\f3ef";
}
.bx-git-merge:before {
    content: "\f3f0";
}
.bx-git-pull-request-closed:before {
    content: "\f3f1";
}
.bx-git-pull-request-draft:before {
    content: "\f3f2";
}
.bx-git-pull-request:before {
    content: "\f3f3";
}
.bx-git-repo-forked:before {
    content: "\f3f4";
}
.bx-glasses-alt:before {
    content: "\f3f5";
}
.bx-glasses:before {
    content: "\f3f6";
}
.bx-globe-africa:before {
    content: "\f3f7";
}
.bx-globe-alt:before {
    content: "\f3f8";
}
.bx-globe-americas:before {
    content: "\f3f9";
}
.bx-globe-antartica:before {
    content: "\f3fa";
}
.bx-globe-asia:before {
    content: "\f3fb";
}
.bx-globe-europe:before {
    content: "\f3fc";
}
.bx-globe-oceania:before {
    content: "\f3fd";
}
.bx-globe-stand:before {
    content: "\f3fe";
}
.bx-globe:before {
    content: "\f3ff";
}
.bx-golf-ball:before {
    content: "\f400";
}
.bx-gradient:before {
    content: "\f401";
}
.bx-grid-9:before {
    content: "\f402";
}
.bx-grid-circle-diagonal-left:before {
    content: "\f403";
}
.bx-grid-circle-diagonal-right:before {
    content: "\f404";
}
.bx-grid-circle-plus:before {
    content: "\f405";
}
.bx-grid-circle:before {
    content: "\f406";
}
.bx-grid-column-left:before {
    content: "\f407";
}
.bx-grid-column-right:before {
    content: "\f408";
}
.bx-grid-plus:before {
    content: "\f409";
}
.bx-grid-row-bottom:before {
    content: "\f40a";
}
.bx-grid-row-top:before {
    content: "\f40b";
}
.bx-grid-search:before {
    content: "\f40c";
}
.bx-grid:before {
    content: "\f40d";
}
.bx-groceries:before {
    content: "\f40e";
}
.bx-group:before {
    content: "\f40f";
}
.bx-guitar-amp:before {
    content: "\f410";
}
.bx-hail:before {
    content: "\f411";
}
.bx-hand-rock:before {
    content: "\f412";
}
.bx-hand:before {
    content: "\f413";
}
.bx-handheld-alt-1:before {
    content: "\f414";
}
.bx-handheld-alt:before {
    content: "\f415";
}
.bx-handheld:before {
    content: "\f416";
}
.bx-handshake:before {
    content: "\f417";
}
.bx-hanger:before {
    content: "\f418";
}
.bx-happy-alt:before {
    content: "\f419";
}
.bx-happy-beaming:before {
    content: "\f41a";
}
.bx-happy-heart-eyes:before {
    content: "\f41b";
}
.bx-happy:before {
    content: "\f41c";
}
.bx-hard-drive:before {
    content: "\f41d";
}
.bx-hard-hat:before {
    content: "\f41e";
}
.bx-hashtag:before {
    content: "\f41f";
}
.bx-hdmi:before {
    content: "\f420";
}
.bx-head:before {
    content: "\f421";
}
.bx-heading-1:before {
    content: "\f422";
}
.bx-heading-2:before {
    content: "\f423";
}
.bx-heading-3:before {
    content: "\f424";
}
.bx-heading:before {
    content: "\f425";
}
.bx-headphone-alt-2:before {
    content: "\f426";
}
.bx-headphone-alt:before {
    content: "\f427";
}
.bx-headphone-mic:before {
    content: "\f428";
}
.bx-headphone:before {
    content: "\f429";
}
.bx-heart-break:before {
    content: "\f42a";
}
.bx-heart-circle:before {
    content: "\f42b";
}
.bx-heart-half:before {
    content: "\f42c";
}
.bx-heart-plus:before {
    content: "\f42d";
}
.bx-heart-square:before {
    content: "\f42e";
}
.bx-heart:before {
    content: "\f42f";
}
.bx-heat-wave:before {
    content: "\f430";
}
.bx-helmet:before {
    content: "\f431";
}
.bx-help-circle:before {
    content: "\f432";
}
.bx-help-octagon:before {
    content: "\f433";
}
.bx-hexagon:before {
    content: "\f434";
}
.bx-high-speed-train:before {
    content: "\f435";
}
.bx-highlight:before {
    content: "\f436";
}
.bx-highlights:before {
    content: "\f437";
}
.bx-history:before {
    content: "\f438";
}
.bx-home-add:before {
    content: "\f439";
}
.bx-home-alt-2:before {
    content: "\f43a";
}
.bx-home-alt-3:before {
    content: "\f43b";
}
.bx-home-alt:before {
    content: "\f43c";
}
.bx-home-circle:before {
    content: "\f43d";
}
.bx-home-heart:before {
    content: "\f43e";
}
.bx-home:before {
    content: "\f43f";
}
.bx-honey:before {
    content: "\f440";
}
.bx-horizon-sea:before {
    content: "\f441";
}
.bx-horizontal-align-center:before {
    content: "\f442";
}
.bx-horizontal-align-left:before {
    content: "\f443";
}
.bx-horizontal-align-right:before {
    content: "\f444";
}
.bx-horizontal-center:before {
    content: "\f445";
}
.bx-horizontal-distribute-center:before {
    content: "\f446";
}
.bx-horizontal-distribute-left:before {
    content: "\f447";
}
.bx-horizontal-distribute-right:before {
    content: "\f448";
}
.bx-horizontal-left:before {
    content: "\f449";
}
.bx-horizontal-right:before {
    content: "\f44a";
}
.bx-horizontal-spacing:before {
    content: "\f44b";
}
.bx-hospital:before {
    content: "\f44c";
}
.bx-hot-tub-water:before {
    content: "\f44d";
}
.bx-hot-tub:before {
    content: "\f44e";
}
.bx-hot:before {
    content: "\f44f";
}
.bx-hourglass:before {
    content: "\f450";
}
.bx-hurricane:before {
    content: "\f451";
}
.bx-icecream:before {
    content: "\f452";
}
.bx-iframe:before {
    content: "\f453";
}
.bx-image-alt:before {
    content: "\f454";
}
.bx-image-circle:before {
    content: "\f455";
}
.bx-image-landscape:before {
    content: "\f456";
}
.bx-image-no-background:before {
    content: "\f457";
}
.bx-image-plus:before {
    content: "\f458";
}
.bx-image-portrait:before {
    content: "\f459";
}
.bx-image:before {
    content: "\f45a";
}
.bx-images:before {
    content: "\f45b";
}
.bx-inbox:before {
    content: "\f45c";
}
.bx-infinite:before {
    content: "\f45d";
}
.bx-info-circle:before {
    content: "\f45e";
}
.bx-info-octagon:before {
    content: "\f45f";
}
.bx-info-shield:before {
    content: "\f460";
}
.bx-info-square:before {
    content: "\f461";
}
.bx-inner-shadow:before {
    content: "\f462";
}
.bx-institution:before {
    content: "\f463";
}
.bx-intellect:before {
    content: "\f464";
}
.bx-invert-adjust:before {
    content: "\f465";
}
.bx-invert:before {
    content: "\f466";
}
.bx-island:before {
    content: "\f467";
}
.bx-italic:before {
    content: "\f468";
}
.bx-joystick-alt:before {
    content: "\f469";
}
.bx-joystick-button:before {
    content: "\f46a";
}
.bx-joystick:before {
    content: "\f46b";
}
.bx-key-alt:before {
    content: "\f46c";
}
.bx-key:before {
    content: "\f46d";
}
.bx-keyboard:before {
    content: "\f46e";
}
.bx-keyframe-ease-in:before {
    content: "\f46f";
}
.bx-keyframe-ease-out:before {
    content: "\f470";
}
.bx-keyframe-easy-ease:before {
    content: "\f471";
}
.bx-keyframe-hold-ease-in:before {
    content: "\f472";
}
.bx-keyframe-hold-ease-out:before {
    content: "\f473";
}
.bx-keyframe-hold-linear-in:before {
    content: "\f474";
}
.bx-keyframe-hold-linear-out:before {
    content: "\f475";
}
.bx-keyframe:before {
    content: "\f476";
}
.bx-knife:before {
    content: "\f477";
}
.bx-landmark:before {
    content: "\f478";
}
.bx-laptop-alt:before {
    content: "\f479";
}
.bx-laptop:before {
    content: "\f47a";
}
.bx-lasso:before {
    content: "\f47b";
}
.bx-last:before {
    content: "\f47c";
}
.bx-laugh:before {
    content: "\f47d";
}
.bx-law:before {
    content: "\f47e";
}
.bx-layers-alt:before {
    content: "\f47f";
}
.bx-layers-down-left:before {
    content: "\f480";
}
.bx-layers-down-right:before {
    content: "\f481";
}
.bx-layers-minus-alt:before {
    content: "\f482";
}
.bx-layers-plus-alt:before {
    content: "\f483";
}
.bx-layers:before {
    content: "\f484";
}
.bx-layout-check:before {
    content: "\f485";
}
.bx-layout-minus:before {
    content: "\f486";
}
.bx-layout-plus:before {
    content: "\f487";
}
.bx-layout-search:before {
    content: "\f488";
}
.bx-layout:before {
    content: "\f489";
}
.bx-leaf-alt:before {
    content: "\f48a";
}
.bx-leaf:before {
    content: "\f48b";
}
.bx-left-indent:before {
    content: "\f48c";
}
.bx-lemon:before {
    content: "\f48d";
}
.bx-letter-spacing-alt:before {
    content: "\f48e";
}
.bx-letter-spacing:before {
    content: "\f48f";
}
.bx-light-bulb-alt:before {
    content: "\f490";
}
.bx-light-bulb-on:before {
    content: "\f491";
}
.bx-light-bulb:before {
    content: "\f492";
}
.bx-lightbulb-alt-2:before {
    content: "\f493";
}
.bx-like:before {
    content: "\f494";
}
.bx-line-chart-square:before {
    content: "\f495";
}
.bx-line-spacing-alt:before {
    content: "\f496";
}
.bx-line-spacing:before {
    content: "\f497";
}
.bx-link-alt:before {
    content: "\f498";
}
.bx-link-break:before {
    content: "\f499";
}
.bx-link:before {
    content: "\f49a";
}
.bx-lira:before {
    content: "\f49b";
}
.bx-list-minus:before {
    content: "\f49c";
}
.bx-list-music:before {
    content: "\f49d";
}
.bx-list-ol:before {
    content: "\f49e";
}
.bx-list-play:before {
    content: "\f49f";
}
.bx-list-plus:before {
    content: "\f4a0";
}
.bx-list-square:before {
    content: "\f4a1";
}
.bx-list-ul-square:before {
    content: "\f4a2";
}
.bx-list-ul:before {
    content: "\f4a3";
}
.bx-list-x:before {
    content: "\f4a4";
}
.bx-list:before {
    content: "\f4a5";
}
.bx-loader-dots:before {
    content: "\f4a6";
}
.bx-loader-lines:before {
    content: "\f4a7";
}
.bx-location-alt-2:before {
    content: "\f4a8";
}
.bx-location-alt:before {
    content: "\f4a9";
}
.bx-location-blank:before {
    content: "\f4aa";
}
.bx-location-check:before {
    content: "\f4ab";
}
.bx-location-pin:before {
    content: "\f4ac";
}
.bx-location-plus:before {
    content: "\f4ad";
}
.bx-location-x:before {
    content: "\f4ae";
}
.bx-location:before {
    content: "\f4af";
}
.bx-lock-keyhole-open-alt:before {
    content: "\f4b0";
}
.bx-lock-keyhole-open:before {
    content: "\f4b1";
}
.bx-lock-keyhole:before {
    content: "\f4b2";
}
.bx-lock-open-alt:before {
    content: "\f4b3";
}
.bx-lock-open:before {
    content: "\f4b4";
}
.bx-lock:before {
    content: "\f4b5";
}
.bx-lotion:before {
    content: "\f4b6";
}
.bx-low-vision:before {
    content: "\f4b7";
}
.bx-lowercase:before {
    content: "\f4b8";
}
.bx-luggage:before {
    content: "\f4b9";
}
.bx-lungs:before {
    content: "\f4ba";
}
.bx-magic-wand:before {
    content: "\f4bb";
}
.bx-magnet:before {
    content: "\f4bc";
}
.bx-mail-open:before {
    content: "\f4bd";
}
.bx-male:before {
    content: "\f4be";
}
.bx-man-woman:before {
    content: "\f4bf";
}
.bx-man:before {
    content: "\f4c0";
}
.bx-map:before {
    content: "\f4c1";
}
.bx-margin-bottom:before {
    content: "\f4c2";
}
.bx-margin-left:before {
    content: "\f4c3";
}
.bx-margin-right:before {
    content: "\f4c4";
}
.bx-margin-top:before {
    content: "\f4c5";
}
.bx-martini:before {
    content: "\f4c6";
}
.bx-mask:before {
    content: "\f4c7";
}
.bx-math-alt:before {
    content: "\f4c8";
}
.bx-math:before {
    content: "\f4c9";
}
.bx-meat:before {
    content: "\f4ca";
}
.bx-medal-alt-2:before {
    content: "\f4cb";
}
.bx-medal-alt:before {
    content: "\f4cc";
}
.bx-medal-star-alt-2:before {
    content: "\f4cd";
}
.bx-medal-star-alt:before {
    content: "\f4ce";
}
.bx-medal-star:before {
    content: "\f4cf";
}
.bx-medal:before {
    content: "\f4d0";
}
.bx-medical-flask:before {
    content: "\f4d1";
}
.bx-medical-kit:before {
    content: "\f4d2";
}
.bx-megaphone-alt:before {
    content: "\f4d3";
}
.bx-megaphone:before {
    content: "\f4d4";
}
.bx-meh-alt:before {
    content: "\f4d5";
}
.bx-meh-blank:before {
    content: "\f4d6";
}
.bx-meh:before {
    content: "\f4d7";
}
.bx-menu-close:before {
    content: "\f4d8";
}
.bx-menu-filter:before {
    content: "\f4d9";
}
.bx-menu-left:before {
    content: "\f4da";
}
.bx-menu-right:before {
    content: "\f4db";
}
.bx-menu-search:before {
    content: "\f4dc";
}
.bx-menu-select:before {
    content: "\f4dd";
}
.bx-menu-wide:before {
    content: "\f4de";
}
.bx-menu-wider:before {
    content: "\f4df";
}
.bx-menu:before {
    content: "\f4e0";
}
.bx-mesh:before {
    content: "\f4e1";
}
.bx-message-bubble-captions:before {
    content: "\f4e2";
}
.bx-message-bubble-check:before {
    content: "\f4e3";
}
.bx-message-bubble-code:before {
    content: "\f4e4";
}
.bx-message-bubble-detail:before {
    content: "\f4e5";
}
.bx-message-bubble-dots-2:before {
    content: "\f4e6";
}
.bx-message-bubble-dots:before {
    content: "\f4e7";
}
.bx-message-bubble-edit:before {
    content: "\f4e8";
}
.bx-message-bubble-exclamation:before {
    content: "\f4e9";
}
.bx-message-bubble-heart:before {
    content: "\f4ea";
}
.bx-message-bubble-image:before {
    content: "\f4eb";
}
.bx-message-bubble-minus:before {
    content: "\f4ec";
}
.bx-message-bubble-notification:before {
    content: "\f4ed";
}
.bx-message-bubble-plus:before {
    content: "\f4ee";
}
.bx-message-bubble-question-mark:before {
    content: "\f4ef";
}
.bx-message-bubble-reply:before {
    content: "\f4f0";
}
.bx-message-bubble-star:before {
    content: "\f4f1";
}
.bx-message-bubble-x:before {
    content: "\f4f2";
}
.bx-message-bubble:before {
    content: "\f4f3";
}
.bx-message-captions:before {
    content: "\f4f4";
}
.bx-message-check:before {
    content: "\f4f5";
}
.bx-message-circle-captions:before {
    content: "\f4f6";
}
.bx-message-circle-check:before {
    content: "\f4f7";
}
.bx-message-circle-code:before {
    content: "\f4f8";
}
.bx-message-circle-detail:before {
    content: "\f4f9";
}
.bx-message-circle-dots-2:before {
    content: "\f4fa";
}
.bx-message-circle-dots:before {
    content: "\f4fb";
}
.bx-message-circle-edit:before {
    content: "\f4fc";
}
.bx-message-circle-exclamation:before {
    content: "\f4fd";
}
.bx-message-circle-heart:before {
    content: "\f4fe";
}
.bx-message-circle-image:before {
    content: "\f4ff";
}
.bx-message-circle-minus:before {
    content: "\f500";
}
.bx-message-circle-notification:before {
    content: "\f501";
}
.bx-message-circle-plus:before {
    content: "\f502";
}
.bx-message-circle-question-mark:before {
    content: "\f503";
}
.bx-message-circle-reply:before {
    content: "\f504";
}
.bx-message-circle-star:before {
    content: "\f505";
}
.bx-message-circle-x:before {
    content: "\f506";
}
.bx-message-circle:before {
    content: "\f507";
}
.bx-message-code:before {
    content: "\f508";
}
.bx-message-detail:before {
    content: "\f509";
}
.bx-message-dots-2:before {
    content: "\f50a";
}
.bx-message-dots:before {
    content: "\f50b";
}
.bx-message-edit:before {
    content: "\f50c";
}
.bx-message-exclamation:before {
    content: "\f50d";
}
.bx-message-heart:before {
    content: "\f50e";
}
.bx-message-image:before {
    content: "\f50f";
}
.bx-message-minus:before {
    content: "\f510";
}
.bx-message-notification:before {
    content: "\f511";
}
.bx-message-plus:before {
    content: "\f512";
}
.bx-message-question-mark:before {
    content: "\f513";
}
.bx-message-reply:before {
    content: "\f514";
}
.bx-message-star:before {
    content: "\f515";
}
.bx-message-x:before {
    content: "\f516";
}
.bx-message:before {
    content: "\f517";
}
.bx-meteor:before {
    content: "\f518";
}
.bx-microchip:before {
    content: "\f519";
}
.bx-microphone-alt-2:before {
    content: "\f51a";
}
.bx-microphone-alt:before {
    content: "\f51b";
}
.bx-microphone-big-alt:before {
    content: "\f51c";
}
.bx-microphone-big:before {
    content: "\f51d";
}
.bx-microphone-slash:before {
    content: "\f51e";
}
.bx-microphone:before {
    content: "\f51f";
}
.bx-microwave-oven:before {
    content: "\f520";
}
.bx-milk-bottle:before {
    content: "\f521";
}
.bx-minus-circle:before {
    content: "\f522";
}
.bx-minus-shield:before {
    content: "\f523";
}
.bx-minus-square:before {
    content: "\f524";
}
.bx-minus:before {
    content: "\f525";
}
.bx-mobile-alt-1:before {
    content: "\f526";
}
.bx-mobile-alt:before {
    content: "\f527";
}
.bx-mobile-back-alt-1:before {
    content: "\f528";
}
.bx-mobile-back-alt:before {
    content: "\f529";
}
.bx-mobile-back:before {
    content: "\f52a";
}
.bx-mobile-ring:before {
    content: "\f52b";
}
.bx-mobile:before {
    content: "\f52c";
}
.bx-moon-crater:before {
    content: "\f52d";
}
.bx-moon-phase-0:before {
    content: "\f52e";
}
.bx-moon-phase-1:before {
    content: "\f52f";
}
.bx-moon-phase-2:before {
    content: "\f530";
}
.bx-moon-phase-3:before {
    content: "\f531";
}
.bx-moon-phase-4:before {
    content: "\f532";
}
.bx-moon-phase-5:before {
    content: "\f533";
}
.bx-moon-phase-6:before {
    content: "\f534";
}
.bx-moon-star:before {
    content: "\f535";
}
.bx-moon:before {
    content: "\f536";
}
.bx-mosque:before {
    content: "\f537";
}
.bx-motion-alt:before {
    content: "\f538";
}
.bx-motion:before {
    content: "\f539";
}
.bx-motorcycle:before {
    content: "\f53a";
}
.bx-mountain-peak:before {
    content: "\f53b";
}
.bx-mountain-view:before {
    content: "\f53c";
}
.bx-mouse-alt:before {
    content: "\f53d";
}
.bx-mouse:before {
    content: "\f53e";
}
.bx-move-diagonal-left:before {
    content: "\f53f";
}
.bx-move-diagonal-right:before {
    content: "\f540";
}
.bx-move-horizontal:before {
    content: "\f541";
}
.bx-move-vertical:before {
    content: "\f542";
}
.bx-move:before {
    content: "\f543";
}
.bx-movie-play:before {
    content: "\f544";
}
.bx-movie:before {
    content: "\f545";
}
.bx-music-alt:before {
    content: "\f546";
}
.bx-music-library:before {
    content: "\f547";
}
.bx-music:before {
    content: "\f548";
}
.bx-network-chart:before {
    content: "\f549";
}
.bx-news:before {
    content: "\f54a";
}
.bx-newspaper:before {
    content: "\f54b";
}
.bx-night-light:before {
    content: "\f54c";
}
.bx-no-entry:before {
    content: "\f54d";
}
.bx-noise:before {
    content: "\f54e";
}
.bx-note-book:before {
    content: "\f54f";
}
.bx-note:before {
    content: "\f550";
}
.bx-notification-slash:before {
    content: "\f551";
}
.bx-notification:before {
    content: "\f552";
}
.bx-nut:before {
    content: "\f553";
}
.bx-octopus:before {
    content: "\f554";
}
.bx-outdoor-dining:before {
    content: "\f555";
}
.bx-outer-shadow:before {
    content: "\f556";
}
.bx-oval-vertical:before {
    content: "\f557";
}
.bx-oval:before {
    content: "\f558";
}
.bx-oven:before {
    content: "\f559";
}
.bx-owl:before {
    content: "\f55a";
}
.bx-package:before {
    content: "\f55b";
}
.bx-pacman:before {
    content: "\f55c";
}
.bx-paint-alt:before {
    content: "\f55d";
}
.bx-paint-roll:before {
    content: "\f55e";
}
.bx-paint:before {
    content: "\f55f";
}
.bx-palette:before {
    content: "\f560";
}
.bx-pant:before {
    content: "\f561";
}
.bx-paper-plane:before {
    content: "\f562";
}
.bx-paperclip:before {
    content: "\f563";
}
.bx-paragraph-spacing:before {
    content: "\f564";
}
.bx-paragraph:before {
    content: "\f565";
}
.bx-parent-child:before {
    content: "\f566";
}
.bx-party:before {
    content: "\f567";
}
.bx-paste:before {
    content: "\f568";
}
.bx-pause-circle:before {
    content: "\f569";
}
.bx-pause:before {
    content: "\f56a";
}
.bx-paw-print:before {
    content: "\f56b";
}
.bx-pear:before {
    content: "\f56c";
}
.bx-pen-alt:before {
    content: "\f56d";
}
.bx-pen-draw:before {
    content: "\f56e";
}
.bx-pen-edit-circle:before {
    content: "\f56f";
}
.bx-pen-minus:before {
    content: "\f570";
}
.bx-pen-plus:before {
    content: "\f571";
}
.bx-pen:before {
    content: "\f572";
}
.bx-pencil-circle:before {
    content: "\f573";
}
.bx-pencil-draw:before {
    content: "\f574";
}
.bx-pencil-edit-circle:before {
    content: "\f575";
}
.bx-pencil-sparkles:before {
    content: "\f576";
}
.bx-pencil-square:before {
    content: "\f577";
}
.bx-pencil:before {
    content: "\f578";
}
.bx-pentagon:before {
    content: "\f579";
}
.bx-people-diversity:before {
    content: "\f57a";
}
.bx-people-handshake:before {
    content: "\f57b";
}
.bx-people-heart:before {
    content: "\f57c";
}
.bx-percentage:before {
    content: "\f57d";
}
.bx-perspective:before {
    content: "\f57e";
}
.bx-petrol-pump:before {
    content: "\f57f";
}
.bx-pharmacy:before {
    content: "\f580";
}
.bx-phone-book:before {
    content: "\f581";
}
.bx-phone-forwarding:before {
    content: "\f582";
}
.bx-phone-incoming:before {
    content: "\f583";
}
.bx-phone-outgoing:before {
    content: "\f584";
}
.bx-phone-plus:before {
    content: "\f585";
}
.bx-phone-ring:before {
    content: "\f586";
}
.bx-phone-x:before {
    content: "\f587";
}
.bx-phone:before {
    content: "\f588";
}
.bx-photo-album:before {
    content: "\f589";
}
.bx-piano-alt:before {
    content: "\f58a";
}
.bx-piano-grand:before {
    content: "\f58b";
}
.bx-piano:before {
    content: "\f58c";
}
.bx-pickup-truck:before {
    content: "\f58d";
}
.bx-picture-in-picture-close:before {
    content: "\f58e";
}
.bx-picture-in-picture:before {
    content: "\f58f";
}
.bx-pie-chart-alt-2:before {
    content: "\f590";
}
.bx-pie-chart-alt:before {
    content: "\f591";
}
.bx-pie-chart:before {
    content: "\f592";
}
.bx-piggy-bank:before {
    content: "\f593";
}
.bx-pill-bottle-alt:before {
    content: "\f594";
}
.bx-pill-bottle:before {
    content: "\f595";
}
.bx-pill:before {
    content: "\f596";
}
.bx-pin-alt:before {
    content: "\f597";
}
.bx-pin-slash-alt:before {
    content: "\f598";
}
.bx-pin:before {
    content: "\f599";
}
.bx-pizza-alt:before {
    content: "\f59a";
}
.bx-pizza:before {
    content: "\f59b";
}
.bx-plane-alt:before {
    content: "\f59c";
}
.bx-plane-land:before {
    content: "\f59d";
}
.bx-plane-take-off:before {
    content: "\f59e";
}
.bx-plane:before {
    content: "\f59f";
}
.bx-planet:before {
    content: "\f5a0";
}
.bx-plant-pot:before {
    content: "\f5a1";
}
.bx-play-circle-alt:before {
    content: "\f5a2";
}
.bx-play-circle:before {
    content: "\f5a3";
}
.bx-play:before {
    content: "\f5a4";
}
.bx-plug-connect:before {
    content: "\f5a5";
}
.bx-plus-big:before {
    content: "\f5a6";
}
.bx-plus-circle:before {
    content: "\f5a7";
}
.bx-plus-shield:before {
    content: "\f5a8";
}
.bx-plus-square:before {
    content: "\f5a9";
}
.bx-plus:before {
    content: "\f5aa";
}
.bx-podcast:before {
    content: "\f5ab";
}
.bx-polar-chart:before {
    content: "\f5ac";
}
.bx-poll:before {
    content: "\f5ad";
}
.bx-polygon:before {
    content: "\f5ae";
}
.bx-popsicle:before {
    content: "\f5af";
}
.bx-pound:before {
    content: "\f5b0";
}
.bx-prawn:before {
    content: "\f5b1";
}
.bx-price-tag-alt:before {
    content: "\f5b2";
}
.bx-price-tag:before {
    content: "\f5b3";
}
.bx-print-dollar:before {
    content: "\f5b4";
}
.bx-printer:before {
    content: "\f5b5";
}
.bx-psychology:before {
    content: "\f5b6";
}
.bx-puck:before {
    content: "\f5b7";
}
.bx-pulse:before {
    content: "\f5b8";
}
.bx-pyramid:before {
    content: "\f5b9";
}
.bx-qr-scan:before {
    content: "\f5ba";
}
.bx-qr:before {
    content: "\f5bb";
}
.bx-queue:before {
    content: "\f5bc";
}
.bx-quote-left-alt:before {
    content: "\f5bd";
}
.bx-quote-left:before {
    content: "\f5be";
}
.bx-quote-right-alt:before {
    content: "\f5bf";
}
.bx-quote-right:before {
    content: "\f5c0";
}
.bx-quote-single-left:before {
    content: "\f5c1";
}
.bx-quote-single-right:before {
    content: "\f5c2";
}
.bx-radar:before {
    content: "\f5c3";
}
.bx-radiation:before {
    content: "\f5c4";
}
.bx-radio-circle-marked:before {
    content: "\f5c5";
}
.bx-radio-circle:before {
    content: "\f5c6";
}
.bx-radio:before {
    content: "\f5c7";
}
.bx-rainbow:before {
    content: "\f5c8";
}
.bx-reading-glass:before {
    content: "\f5c9";
}
.bx-reading:before {
    content: "\f5ca";
}
.bx-receipt:before {
    content: "\f5cb";
}
.bx-rectangle-vertical:before {
    content: "\f5cc";
}
.bx-rectangle-wide:before {
    content: "\f5cd";
}
.bx-rectangle:before {
    content: "\f5ce";
}
.bx-recycle:before {
    content: "\f5cf";
}
.bx-redo-alt:before {
    content: "\f5d0";
}
.bx-redo-stroke-alt:before {
    content: "\f5d1";
}
.bx-redo-stroke:before {
    content: "\f5d2";
}
.bx-redo:before {
    content: "\f5d3";
}
.bx-refresh-ccw-alt-dot:before {
    content: "\f5d4";
}
.bx-refresh-ccw-alt:before {
    content: "\f5d5";
}
.bx-refresh-ccw-dot:before {
    content: "\f5d6";
}
.bx-refresh-ccw:before {
    content: "\f5d7";
}
.bx-refresh-cw-alt-dot:before {
    content: "\f5d8";
}
.bx-refresh-cw-alt:before {
    content: "\f5d9";
}
.bx-refresh-cw-dot:before {
    content: "\f5da";
}
.bx-refresh-cw:before {
    content: "\f5db";
}
.bx-registered:before {
    content: "\f5dc";
}
.bx-rename:before {
    content: "\f5dd";
}
.bx-repeat-alt-2:before {
    content: "\f5de";
}
.bx-repeat-alt:before {
    content: "\f5df";
}
.bx-repeat:before {
    content: "\f5e0";
}
.bx-reply-big:before {
    content: "\f5e1";
}
.bx-reply-stroke:before {
    content: "\f5e2";
}
.bx-reply:before {
    content: "\f5e3";
}
.bx-report:before {
    content: "\f5e4";
}
.bx-rewind-circle:before {
    content: "\f5e5";
}
.bx-rewind:before {
    content: "\f5e6";
}
.bx-rfid:before {
    content: "\f5e7";
}
.bx-rgb:before {
    content: "\f5e8";
}
.bx-right-angle-triangle-half:before {
    content: "\f5e9";
}
.bx-right-angle-triangle:before {
    content: "\f5ea";
}
.bx-right-indent:before {
    content: "\f5eb";
}
.bx-robot:before {
    content: "\f5ec";
}
.bx-rocket-alt:before {
    content: "\f5ed";
}
.bx-rocket:before {
    content: "\f5ee";
}
.bx-rotate-ccw-10:before {
    content: "\f5ef";
}
.bx-rotate-ccw-30:before {
    content: "\f5f0";
}
.bx-rotate-ccw-5:before {
    content: "\f5f1";
}
.bx-rotate-ccw-dot:before {
    content: "\f5f2";
}
.bx-rotate-ccw:before {
    content: "\f5f3";
}
.bx-rotate-cw-10:before {
    content: "\f5f4";
}
.bx-rotate-cw-30:before {
    content: "\f5f5";
}
.bx-rotate-cw-5:before {
    content: "\f5f6";
}
.bx-rotate-cw-dot:before {
    content: "\f5f7";
}
.bx-rotate-cw:before {
    content: "\f5f8";
}
.bx-rotate-square-ccw:before {
    content: "\f5f9";
}
.bx-rotate-square-cw:before {
    content: "\f5fa";
}
.bx-row-resize:before {
    content: "\f5fb";
}
.bx-rows-3:before {
    content: "\f5fc";
}
.bx-rows-4:before {
    content: "\f5fd";
}
.bx-rows:before {
    content: "\f5fe";
}
.bx-rss:before {
    content: "\f5ff";
}
.bx-ruble:before {
    content: "\f600";
}
.bx-rugby-ball:before {
    content: "\f601";
}
.bx-ruler:before {
    content: "\f602";
}
.bx-running:before {
    content: "\f603";
}
.bx-rupee:before {
    content: "\f604";
}
.bx-sad:before {
    content: "\f605";
}
.bx-safe:before {
    content: "\f606";
}
.bx-sandwich:before {
    content: "\f607";
}
.bx-sapling:before {
    content: "\f608";
}
.bx-save:before {
    content: "\f609";
}
.bx-scale:before {
    content: "\f60a";
}
.bx-scan-ar:before {
    content: "\f60b";
}
.bx-scan-barcode:before {
    content: "\f60c";
}
.bx-scan-detail:before {
    content: "\f60d";
}
.bx-scan-face:before {
    content: "\f60e";
}
.bx-scan-search:before {
    content: "\f60f";
}
.bx-scan:before {
    content: "\f610";
}
.bx-school-bus:before {
    content: "\f611";
}
.bx-school:before {
    content: "\f612";
}
.bx-science:before {
    content: "\f613";
}
.bx-scooter-delivery:before {
    content: "\f614";
}
.bx-scooter:before {
    content: "\f615";
}
.bx-screen-light:before {
    content: "\f616";
}
.bx-screenshot:before {
    content: "\f617";
}
.bx-scroll:before {
    content: "\f618";
}
.bx-sd-card:before {
    content: "\f619";
}
.bx-sea-view:before {
    content: "\f61a";
}
.bx-seal:before {
    content: "\f61b";
}
.bx-search-alt:before {
    content: "\f61c";
}
.bx-search-big:before {
    content: "\f61d";
}
.bx-search-code:before {
    content: "\f61e";
}
.bx-search-minus:before {
    content: "\f61f";
}
.bx-search-plus:before {
    content: "\f620";
}
.bx-search:before {
    content: "\f621";
}
.bx-select-all:before {
    content: "\f622";
}
.bx-select-many:before {
    content: "\f623";
}
.bx-select-none:before {
    content: "\f624";
}
.bx-select:before {
    content: "\f625";
}
.bx-send-alt-2:before {
    content: "\f626";
}
.bx-send-alt:before {
    content: "\f627";
}
.bx-send:before {
    content: "\f628";
}
.bx-server:before {
    content: "\f629";
}
.bx-shadows:before {
    content: "\f62a";
}
.bx-shape-exclude-alt:before {
    content: "\f62b";
}
.bx-shape-exclude:before {
    content: "\f62c";
}
.bx-shape-intersect-alt:before {
    content: "\f62d";
}
.bx-shape-intersect:before {
    content: "\f62e";
}
.bx-shape-outline-alt:before {
    content: "\f62f";
}
.bx-shape-outline:before {
    content: "\f630";
}
.bx-shape-rotate-ccw:before {
    content: "\f631";
}
.bx-shape-rotate-cw:before {
    content: "\f632";
}
.bx-shape-subtract-alt:before {
    content: "\f633";
}
.bx-shape-subtract:before {
    content: "\f634";
}
.bx-shape-trim-alt:before {
    content: "\f635";
}
.bx-shape-trim:before {
    content: "\f636";
}
.bx-shape-unite-alt:before {
    content: "\f637";
}
.bx-shape-unite:before {
    content: "\f638";
}
.bx-shapes-alt:before {
    content: "\f639";
}
.bx-shapes:before {
    content: "\f63a";
}
.bx-share:before {
    content: "\f63b";
}
.bx-shekel:before {
    content: "\f63c";
}
.bx-shield-alt-2:before {
    content: "\f63d";
}
.bx-shield-alt:before {
    content: "\f63e";
}
.bx-shield-circle:before {
    content: "\f63f";
}
.bx-shield-half:before {
    content: "\f640";
}
.bx-shield-quarter:before {
    content: "\f641";
}
.bx-shield:before {
    content: "\f642";
}
.bx-ship:before {
    content: "\f643";
}
.bx-shocked:before {
    content: "\f644";
}
.bx-shopping-bag-alt:before {
    content: "\f645";
}
.bx-shopping-bag:before {
    content: "\f646";
}
.bx-shower:before {
    content: "\f647";
}
.bx-shrink-left:before {
    content: "\f648";
}
.bx-shrink-right:before {
    content: "\f649";
}
.bx-shuffle:before {
    content: "\f64a";
}
.bx-shutter-alt:before {
    content: "\f64b";
}
.bx-shutter:before {
    content: "\f64c";
}
.bx-shuttlecock:before {
    content: "\f64d";
}
.bx-sidebar-right:before {
    content: "\f64e";
}
.bx-sidebar:before {
    content: "\f64f";
}
.bx-signal-1:before {
    content: "\f650";
}
.bx-signal-2:before {
    content: "\f651";
}
.bx-signal-3:before {
    content: "\f652";
}
.bx-signal-4:before {
    content: "\f653";
}
.bx-signal-5:before {
    content: "\f654";
}
.bx-signal-slash:before {
    content: "\f655";
}
.bx-signature:before {
    content: "\f656";
}
.bx-siren-alt:before {
    content: "\f657";
}
.bx-siren:before {
    content: "\f658";
}
.bx-sitemap:before {
    content: "\f659";
}
.bx-size-distort:before {
    content: "\f65a";
}
.bx-size-freeform:before {
    content: "\f65b";
}
.bx-size-uniform:before {
    content: "\f65c";
}
.bx-skateboard:before {
    content: "\f65d";
}
.bx-skip-next-circle:before {
    content: "\f65e";
}
.bx-skip-next:before {
    content: "\f65f";
}
.bx-skip-previous-circle:before {
    content: "\f660";
}
.bx-skip-previous:before {
    content: "\f661";
}
.bx-skirt:before {
    content: "\f662";
}
.bx-sleepy:before {
    content: "\f663";
}
.bx-slice:before {
    content: "\f664";
}
.bx-slider-alt:before {
    content: "\f665";
}
.bx-slider-vertical-alt:before {
    content: "\f666";
}
.bx-slider-vertical:before {
    content: "\f667";
}
.bx-slider:before {
    content: "\f668";
}
.bx-slideshow:before {
    content: "\f669";
}
.bx-smile:before {
    content: "\f66a";
}
.bx-smoke-alarm:before {
    content: "\f66b";
}
.bx-sneaker:before {
    content: "\f66c";
}
.bx-snowflake:before {
    content: "\f66d";
}
.bx-sock:before {
    content: "\f66e";
}
.bx-solar-panel:before {
    content: "\f66f";
}
.bx-spa:before {
    content: "\f670";
}
.bx-spacebar:before {
    content: "\f671";
}
.bx-spade:before {
    content: "\f672";
}
.bx-spanner:before {
    content: "\f673";
}
.bx-sparkle-circle:before {
    content: "\f674";
}
.bx-sparkle-square:before {
    content: "\f675";
}
.bx-sparkle:before {
    content: "\f676";
}
.bx-sparkles-alt:before {
    content: "\f677";
}
.bx-sparkles:before {
    content: "\f678";
}
.bx-speaker:before {
    content: "\f679";
}
.bx-split:before {
    content: "\f67a";
}
.bx-spoon:before {
    content: "\f67b";
}
.bx-spray-can:before {
    content: "\f67c";
}
.bx-square-dashed-half:before {
    content: "\f67d";
}
.bx-square-dashed:before {
    content: "\f67e";
}
.bx-square-rounded:before {
    content: "\f67f";
}
.bx-square-small:before {
    content: "\f680";
}
.bx-square:before {
    content: "\f681";
}
.bx-squircle:before {
    content: "\f682";
}
.bx-stadium:before {
    content: "\f683";
}
.bx-stamp:before {
    content: "\f684";
}
.bx-star-circle:before {
    content: "\f685";
}
.bx-star-half:before {
    content: "\f686";
}
.bx-star-square:before {
    content: "\f687";
}
.bx-star:before {
    content: "\f688";
}
.bx-station:before {
    content: "\f689";
}
.bx-steering-wheel:before {
    content: "\f68a";
}
.bx-steps-down:before {
    content: "\f68b";
}
.bx-steps-up:before {
    content: "\f68c";
}
.bx-sticker:before {
    content: "\f68d";
}
.bx-stop-circle:before {
    content: "\f68e";
}
.bx-stop:before {
    content: "\f68f";
}
.bx-stopwatch:before {
    content: "\f690";
}
.bx-store-alt-2:before {
    content: "\f691";
}
.bx-store-alt:before {
    content: "\f692";
}
.bx-store:before {
    content: "\f693";
}
.bx-strategy:before {
    content: "\f694";
}
.bx-street-view:before {
    content: "\f695";
}
.bx-strikethrough:before {
    content: "\f696";
}
.bx-stroke-drawing:before {
    content: "\f697";
}
.bx-stroke-freehand:before {
    content: "\f698";
}
.bx-stroke-ink:before {
    content: "\f699";
}
.bx-stroke-pen:before {
    content: "\f69a";
}
.bx-subscript:before {
    content: "\f69b";
}
.bx-subway:before {
    content: "\f69c";
}
.bx-sun-bright:before {
    content: "\f69d";
}
.bx-sun-dim:before {
    content: "\f69e";
}
.bx-sun-drizzle:before {
    content: "\f69f";
}
.bx-sun-fog:before {
    content: "\f6a0";
}
.bx-sun-rain-wind:before {
    content: "\f6a1";
}
.bx-sun-rain:before {
    content: "\f6a2";
}
.bx-sun-rise:before {
    content: "\f6a3";
}
.bx-sun-set:before {
    content: "\f6a4";
}
.bx-sun-snow:before {
    content: "\f6a5";
}
.bx-sun:before {
    content: "\f6a6";
}
.bx-superscript:before {
    content: "\f6a7";
}
.bx-surfboard:before {
    content: "\f6a8";
}
.bx-sushi:before {
    content: "\f6a9";
}
.bx-swap-diagonal:before {
    content: "\f6aa";
}
.bx-swap-horizontal:before {
    content: "\f6ab";
}
.bx-swap-vertical:before {
    content: "\f6ac";
}
.bx-swatch:before {
    content: "\f6ad";
}
.bx-swimming-pool:before {
    content: "\f6ae";
}
.bx-swimming:before {
    content: "\f6af";
}
.bx-sword-alt:before {
    content: "\f6b0";
}
.bx-sword:before {
    content: "\f6b1";
}
.bx-syringe:before {
    content: "\f6b2";
}
.bx-t-shirt:before {
    content: "\f6b3";
}
.bx-tab:before {
    content: "\f6b4";
}
.bx-table-cells-large:before {
    content: "\f6b5";
}
.bx-table-cells:before {
    content: "\f6b6";
}
.bx-table-columns-merge:before {
    content: "\f6b7";
}
.bx-table-columns-split:before {
    content: "\f6b8";
}
.bx-table-columns:before {
    content: "\f6b9";
}
.bx-table-layout:before {
    content: "\f6ba";
}
.bx-table-list:before {
    content: "\f6bb";
}
.bx-table-rows-merge:before {
    content: "\f6bc";
}
.bx-table-rows-split:before {
    content: "\f6bd";
}
.bx-table-rows:before {
    content: "\f6be";
}
.bx-table-tennis:before {
    content: "\f6bf";
}
.bx-table:before {
    content: "\f6c0";
}
.bx-tablet:before {
    content: "\f6c1";
}
.bx-tabs:before {
    content: "\f6c2";
}
.bx-tachometer-alt:before {
    content: "\f6c3";
}
.bx-tachometer:before {
    content: "\f6c4";
}
.bx-taco:before {
    content: "\f6c5";
}
.bx-tag-alt:before {
    content: "\f6c6";
}
.bx-tag-x:before {
    content: "\f6c7";
}
.bx-tag:before {
    content: "\f6c8";
}
.bx-takeaway:before {
    content: "\f6c9";
}
.bx-target:before {
    content: "\f6ca";
}
.bx-taxi:before {
    content: "\f6cb";
}
.bx-temple:before {
    content: "\f6cc";
}
.bx-tennis-ball-alt:before {
    content: "\f6cd";
}
.bx-tennis-ball:before {
    content: "\f6ce";
}
.bx-tennis:before {
    content: "\f6cf";
}
.bx-tent:before {
    content: "\f6d0";
}
.bx-terminal:before {
    content: "\f6d1";
}
.bx-test-tube:before {
    content: "\f6d2";
}
.bx-text-height:before {
    content: "\f6d3";
}
.bx-text-underline:before {
    content: "\f6d4";
}
.bx-text-width:before {
    content: "\f6d5";
}
.bx-texture:before {
    content: "\f6d6";
}
.bx-thermometer:before {
    content: "\f6d7";
}
.bx-thread-roll:before {
    content: "\f6d8";
}
.bx-thunder:before {
    content: "\f6d9";
}
.bx-ticket-star:before {
    content: "\f6da";
}
.bx-ticket:before {
    content: "\f6db";
}
.bx-tickets:before {
    content: "\f6dc";
}
.bx-timer:before {
    content: "\f6dd";
}
.bx-tiny-home:before {
    content: "\f6de";
}
.bx-tired:before {
    content: "\f6df";
}
.bx-toggle-left:before {
    content: "\f6e0";
}
.bx-toggle-right:before {
    content: "\f6e1";
}
.bx-toggles:before {
    content: "\f6e2";
}
.bx-toilet-roll:before {
    content: "\f6e3";
}
.bx-tooth:before {
    content: "\f6e4";
}
.bx-torch:before {
    content: "\f6e5";
}
.bx-tornado:before {
    content: "\f6e6";
}
.bx-torus:before {
    content: "\f6e7";
}
.bx-towel:before {
    content: "\f6e8";
}
.bx-toy-car:before {
    content: "\f6e9";
}
.bx-traffic-barrier:before {
    content: "\f6ea";
}
.bx-traffic-cone:before {
    content: "\f6eb";
}
.bx-train:before {
    content: "\f6ec";
}
.bx-tram:before {
    content: "\f6ed";
}
.bx-transgender:before {
    content: "\f6ee";
}
.bx-translate:before {
    content: "\f6ef";
}
.bx-transparency:before {
    content: "\f6f0";
}
.bx-trash-alt:before {
    content: "\f6f1";
}
.bx-trash-x:before {
    content: "\f6f2";
}
.bx-trash:before {
    content: "\f6f3";
}
.bx-treasure-chest:before {
    content: "\f6f4";
}
.bx-tree-alt:before {
    content: "\f6f5";
}
.bx-tree:before {
    content: "\f6f6";
}
.bx-trees:before {
    content: "\f6f7";
}
.bx-trending-down:before {
    content: "\f6f8";
}
.bx-trending-up:before {
    content: "\f6f9";
}
.bx-triangle-half:before {
    content: "\f6fa";
}
.bx-triangle:before {
    content: "\f6fb";
}
.bx-trip:before {
    content: "\f6fc";
}
.bx-trophy-star:before {
    content: "\f6fd";
}
.bx-trophy:before {
    content: "\f6fe";
}
.bx-truck:before {
    content: "\f6ff";
}
.bx-turkey-meat:before {
    content: "\f700";
}
.bx-turn-down:before {
    content: "\f701";
}
.bx-turn-left:before {
    content: "\f702";
}
.bx-turn-right:before {
    content: "\f703";
}
.bx-turn-up:before {
    content: "\f704";
}
.bx-tv-alt:before {
    content: "\f705";
}
.bx-tv:before {
    content: "\f706";
}
.bx-ufo:before {
    content: "\f707";
}
.bx-umbrella-alt:before {
    content: "\f708";
}
.bx-umbrella:before {
    content: "\f709";
}
.bx-underline-dashed:before {
    content: "\f70a";
}
.bx-underline-dotted:before {
    content: "\f70b";
}
.bx-underline-wavy:before {
    content: "\f70c";
}
.bx-underline:before {
    content: "\f70d";
}
.bx-undershirt:before {
    content: "\f70e";
}
.bx-undo-alt:before {
    content: "\f70f";
}
.bx-undo-stroke-alt:before {
    content: "\f710";
}
.bx-undo-stroke:before {
    content: "\f711";
}
.bx-undo:before {
    content: "\f712";
}
.bx-universal-access:before {
    content: "\f713";
}
.bx-unlink-alt:before {
    content: "\f714";
}
.bx-unlink:before {
    content: "\f715";
}
.bx-uppercase:before {
    content: "\f716";
}
.bx-upside-down:before {
    content: "\f717";
}
.bx-usb:before {
    content: "\f718";
}
.bx-user-check:before {
    content: "\f719";
}
.bx-user-circle:before {
    content: "\f71a";
}
.bx-user-hexagon:before {
    content: "\f71b";
}
.bx-user-id-card:before {
    content: "\f71c";
}
.bx-user-minus:before {
    content: "\f71d";
}
.bx-user-plus:before {
    content: "\f71e";
}
.bx-user-search:before {
    content: "\f71f";
}
.bx-user-square:before {
    content: "\f720";
}
.bx-user-voice:before {
    content: "\f721";
}
.bx-user-x:before {
    content: "\f722";
}
.bx-user:before {
    content: "\f723";
}
.bx-van:before {
    content: "\f724";
}
.bx-vector-square:before {
    content: "\f725";
}
.bx-vector-triangle:before {
    content: "\f726";
}
.bx-vector:before {
    content: "\f727";
}
.bx-vertical-align-bottom:before {
    content: "\f728";
}
.bx-vertical-align-center:before {
    content: "\f729";
}
.bx-vertical-align-top:before {
    content: "\f72a";
}
.bx-vertical-bottom:before {
    content: "\f72b";
}
.bx-vertical-center:before {
    content: "\f72c";
}
.bx-vertical-distribute-bottom:before {
    content: "\f72d";
}
.bx-vertical-distribute-center:before {
    content: "\f72e";
}
.bx-vertical-distribute-top:before {
    content: "\f72f";
}
.bx-vertical-spacing:before {
    content: "\f730";
}
.bx-vertical-top:before {
    content: "\f731";
}
.bx-vial-alt:before {
    content: "\f732";
}
.bx-vial:before {
    content: "\f733";
}
.bx-video-cinema:before {
    content: "\f734";
}
.bx-video-plus:before {
    content: "\f735";
}
.bx-video-slash:before {
    content: "\f736";
}
.bx-video:before {
    content: "\f737";
}
.bx-vignette:before {
    content: "\f738";
}
.bx-virus-slash:before {
    content: "\f739";
}
.bx-virus:before {
    content: "\f73a";
}
.bx-voicemail:before {
    content: "\f73b";
}
.bx-volleyball:before {
    content: "\f73c";
}
.bx-volume-full:before {
    content: "\f73d";
}
.bx-volume-low:before {
    content: "\f73e";
}
.bx-volume-mute:before {
    content: "\f73f";
}
.bx-volume:before {
    content: "\f740";
}
.bx-vr-goggles:before {
    content: "\f741";
}
.bx-vr-headset:before {
    content: "\f742";
}
.bx-walking:before {
    content: "\f743";
}
.bx-wall:before {
    content: "\f744";
}
.bx-wallet-alt:before {
    content: "\f745";
}
.bx-wallet-cards:before {
    content: "\f746";
}
.bx-wallet-note:before {
    content: "\f747";
}
.bx-wallet:before {
    content: "\f748";
}
.bx-warehouse:before {
    content: "\f749";
}
.bx-washer:before {
    content: "\f74a";
}
.bx-water-drop-alt:before {
    content: "\f74b";
}
.bx-water-drop-half:before {
    content: "\f74c";
}
.bx-water-drop:before {
    content: "\f74d";
}
.bx-water-spray:before {
    content: "\f74e";
}
.bx-water:before {
    content: "\f74f";
}
.bx-watermelon:before {
    content: "\f750";
}
.bx-waveform:before {
    content: "\f751";
}
.bx-webcam:before {
    content: "\f752";
}
.bx-whiteboard-alt:before {
    content: "\f753";
}
.bx-whiteboard:before {
    content: "\f754";
}
.bx-widget-horizontal:before {
    content: "\f755";
}
.bx-widget-small:before {
    content: "\f756";
}
.bx-widget-vertical:before {
    content: "\f757";
}
.bx-widget:before {
    content: "\f758";
}
.bx-wifi-0:before {
    content: "\f759";
}
.bx-wifi-1:before {
    content: "\f75a";
}
.bx-wifi-2:before {
    content: "\f75b";
}
.bx-wifi-slash:before {
    content: "\f75c";
}
.bx-wifi:before {
    content: "\f75d";
}
.bx-wind:before {
    content: "\f75e";
}
.bx-window-arrow-in:before {
    content: "\f75f";
}
.bx-window-arrow-out:before {
    content: "\f760";
}
.bx-window-mac-alt:before {
    content: "\f761";
}
.bx-window-mac:before {
    content: "\f762";
}
.bx-window:before {
    content: "\f763";
}
.bx-windows:before {
    content: "\f764";
}
.bx-wine-alt:before {
    content: "\f765";
}
.bx-wine:before {
    content: "\f766";
}
.bx-wink-smile:before {
    content: "\f767";
}
.bx-wink-tongue:before {
    content: "\f768";
}
.bx-woman:before {
    content: "\f769";
}
.bx-won:before {
    content: "\f76a";
}
.bx-wrist-watch-alt:before {
    content: "\f76b";
}
.bx-wrist-watch-round-alt:before {
    content: "\f76c";
}
.bx-wrist-watch-round:before {
    content: "\f76d";
}
.bx-wrist-watch:before {
    content: "\f76e";
}
.bx-x-circle:before {
    content: "\f76f";
}
.bx-x-shield:before {
    content: "\f770";
}
.bx-x-square:before {
    content: "\f771";
}
.bx-x:before {
    content: "\f772";
}
.bx-yarn-ball:before {
    content: "\f773";
}
.bx-yen:before {
    content: "\f774";
}
.bx-yin-yang:before {
    content: "\f775";
}
.bxs-8-ball:before {
    content: "\f776";
}
.bxs-a-arrow-down:before {
    content: "\f777";
}
.bxs-a-arrow-up:before {
    content: "\f778";
}
.bxs-accessibility:before {
    content: "\f779";
}
.bxs-acorn:before {
    content: "\f77a";
}
.bxs-address-book:before {
    content: "\f77b";
}
.bxs-air-conditioner:before {
    content: "\f77c";
}
.bxs-air:before {
    content: "\f77d";
}
.bxs-airplay:before {
    content: "\f77e";
}
.bxs-alarm-alt:before {
    content: "\f77f";
}
.bxs-alarm-check:before {
    content: "\f780";
}
.bxs-alarm-exclamation:before {
    content: "\f781";
}
.bxs-alarm-minus:before {
    content: "\f782";
}
.bxs-alarm-plus:before {
    content: "\f783";
}
.bxs-alarm-slash:before {
    content: "\f784";
}
.bxs-alarm-z:before {
    content: "\f785";
}
.bxs-alarm:before {
    content: "\f786";
}
.bxs-album-covers:before {
    content: "\f787";
}
.bxs-alert-circle:before {
    content: "\f788";
}
.bxs-alert-octagon:before {
    content: "\f789";
}
.bxs-alert-shield:before {
    content: "\f78a";
}
.bxs-alert-square:before {
    content: "\f78b";
}
.bxs-alert-triangle:before {
    content: "\f78c";
}
.bxs-alien:before {
    content: "\f78d";
}
.bxs-align-center:before {
    content: "\f78e";
}
.bxs-align-justify:before {
    content: "\f78f";
}
.bxs-align-left:before {
    content: "\f790";
}
.bxs-align-right:before {
    content: "\f791";
}
.bxs-ambulance:before {
    content: "\f792";
}
.bxs-ampersand:before {
    content: "\f793";
}
.bxs-analyze:before {
    content: "\f794";
}
.bxs-anchor:before {
    content: "\f795";
}
.bxs-angle:before {
    content: "\f796";
}
.bxs-angry:before {
    content: "\f797";
}
.bxs-animation-bounce:before {
    content: "\f798";
}
.bxs-apartment:before {
    content: "\f799";
}
.bxs-approximate:before {
    content: "\f79a";
}
.bxs-apps-alt:before {
    content: "\f79b";
}
.bxs-apps:before {
    content: "\f79c";
}
.bxs-arch:before {
    content: "\f79d";
}
.bxs-archive-alt:before {
    content: "\f79e";
}
.bxs-archive-arrow-down:before {
    content: "\f79f";
}
.bxs-archive-arrow-up:before {
    content: "\f7a0";
}
.bxs-archive:before {
    content: "\f7a1";
}
.bxs-area:before {
    content: "\f7a2";
}
.bxs-arrow-big-down-line:before {
    content: "\f7a3";
}
.bxs-arrow-big-down:before {
    content: "\f7a4";
}
.bxs-arrow-big-left-line:before {
    content: "\f7a5";
}
.bxs-arrow-big-left:before {
    content: "\f7a6";
}
.bxs-arrow-big-right-line:before {
    content: "\f7a7";
}
.bxs-arrow-big-right:before {
    content: "\f7a8";
}
.bxs-arrow-big-up-line:before {
    content: "\f7a9";
}
.bxs-arrow-big-up:before {
    content: "\f7aa";
}
.bxs-arrow-down-circle:before {
    content: "\f7ab";
}
.bxs-arrow-down-left-circle:before {
    content: "\f7ac";
}
.bxs-arrow-down-left-square:before {
    content: "\f7ad";
}
.bxs-arrow-down-left-stroke-circle:before {
    content: "\f7ae";
}
.bxs-arrow-down-left-stroke-square:before {
    content: "\f7af";
}
.bxs-arrow-down-left-stroke:before {
    content: "\f7b0";
}
.bxs-arrow-down-left:before {
    content: "\f7b1";
}
.bxs-arrow-down-right-circle:before {
    content: "\f7b2";
}
.bxs-arrow-down-right-square:before {
    content: "\f7b3";
}
.bxs-arrow-down-right-stroke-circle:before {
    content: "\f7b4";
}
.bxs-arrow-down-right-stroke-square:before {
    content: "\f7b5";
}
.bxs-arrow-down-right-stroke:before {
    content: "\f7b6";
}
.bxs-arrow-down-right:before {
    content: "\f7b7";
}
.bxs-arrow-down-square:before {
    content: "\f7b8";
}
.bxs-arrow-down-stroke-circle:before {
    content: "\f7b9";
}
.bxs-arrow-down-stroke-square:before {
    content: "\f7ba";
}
.bxs-arrow-down-stroke:before {
    content: "\f7bb";
}
.bxs-arrow-down:before {
    content: "\f7bc";
}
.bxs-arrow-from-bottom-stroke:before {
    content: "\f7bd";
}
.bxs-arrow-from-left-stroke:before {
    content: "\f7be";
}
.bxs-arrow-from-right-stroke:before {
    content: "\f7bf";
}
.bxs-arrow-from-top-stroke:before {
    content: "\f7c0";
}
.bxs-arrow-in-down-left-circle:before {
    content: "\f7c1";
}
.bxs-arrow-in-down-left-square:before {
    content: "\f7c2";
}
.bxs-arrow-in-down-left-stroke-circle:before {
    content: "\f7c3";
}
.bxs-arrow-in-down-left-stroke-square:before {
    content: "\f7c4";
}
.bxs-arrow-in-down-right-circle:before {
    content: "\f7c5";
}
.bxs-arrow-in-down-right-square:before {
    content: "\f7c6";
}
.bxs-arrow-in-down-right-stroke-circle:before {
    content: "\f7c7";
}
.bxs-arrow-in-down-right-stroke-square:before {
    content: "\f7c8";
}
.bxs-arrow-in-down-square-half:before {
    content: "\f7c9";
}
.bxs-arrow-in-left-square-half:before {
    content: "\f7ca";
}
.bxs-arrow-in-right-square-half:before {
    content: "\f7cb";
}
.bxs-arrow-in-up-left-circle:before {
    content: "\f7cc";
}
.bxs-arrow-in-up-left-square:before {
    content: "\f7cd";
}
.bxs-arrow-in-up-left-stroke-circle:before {
    content: "\f7ce";
}
.bxs-arrow-in-up-left-stroke-square:before {
    content: "\f7cf";
}
.bxs-arrow-in-up-right-circle:before {
    content: "\f7d0";
}
.bxs-arrow-in-up-right-square:before {
    content: "\f7d1";
}
.bxs-arrow-in-up-right-stroke-circle:before {
    content: "\f7d2";
}
.bxs-arrow-in-up-right-stroke-square:before {
    content: "\f7d3";
}
.bxs-arrow-in-up-square-half:before {
    content: "\f7d4";
}
.bxs-arrow-left-circle:before {
    content: "\f7d5";
}
.bxs-arrow-left-square:before {
    content: "\f7d6";
}
.bxs-arrow-left-stroke-circle:before {
    content: "\f7d7";
}
.bxs-arrow-left-stroke-square:before {
    content: "\f7d8";
}
.bxs-arrow-left-stroke:before {
    content: "\f7d9";
}
.bxs-arrow-left:before {
    content: "\f7da";
}
.bxs-arrow-out-down-left-circle:before {
    content: "\f7db";
}
.bxs-arrow-out-down-left-square:before {
    content: "\f7dc";
}
.bxs-arrow-out-down-left-stroke-circle:before {
    content: "\f7dd";
}
.bxs-arrow-out-down-left-stroke-square:before {
    content: "\f7de";
}
.bxs-arrow-out-down-right-circle:before {
    content: "\f7df";
}
.bxs-arrow-out-down-right-square:before {
    content: "\f7e0";
}
.bxs-arrow-out-down-right-stroke-circle:before {
    content: "\f7e1";
}
.bxs-arrow-out-down-right-stroke-square:before {
    content: "\f7e2";
}
.bxs-arrow-out-down-square-half:before {
    content: "\f7e3";
}
.bxs-arrow-out-left-square-half:before {
    content: "\f7e4";
}
.bxs-arrow-out-right-square-half:before {
    content: "\f7e5";
}
.bxs-arrow-out-up-left-circle:before {
    content: "\f7e6";
}
.bxs-arrow-out-up-left-square:before {
    content: "\f7e7";
}
.bxs-arrow-out-up-left-stroke-circle:before {
    content: "\f7e8";
}
.bxs-arrow-out-up-left-stroke-square:before {
    content: "\f7e9";
}
.bxs-arrow-out-up-right-circle:before {
    content: "\f7ea";
}
.bxs-arrow-out-up-right-square:before {
    content: "\f7eb";
}
.bxs-arrow-out-up-right-stroke-circle:before {
    content: "\f7ec";
}
.bxs-arrow-out-up-right-stroke-square:before {
    content: "\f7ed";
}
.bxs-arrow-out-up-square-half:before {
    content: "\f7ee";
}
.bxs-arrow-right-circle:before {
    content: "\f7ef";
}
.bxs-arrow-right-square:before {
    content: "\f7f0";
}
.bxs-arrow-right-stroke-circle:before {
    content: "\f7f1";
}
.bxs-arrow-right-stroke-square:before {
    content: "\f7f2";
}
.bxs-arrow-right-stroke:before {
    content: "\f7f3";
}
.bxs-arrow-right:before {
    content: "\f7f4";
}
.bxs-arrow-s-down:before {
    content: "\f7f5";
}
.bxs-arrow-s-left:before {
    content: "\f7f6";
}
.bxs-arrow-s-right:before {
    content: "\f7f7";
}
.bxs-arrow-s-up:before {
    content: "\f7f8";
}
.bxs-arrow-to-bottom-stroke:before {
    content: "\f7f9";
}
.bxs-arrow-to-left-stroke:before {
    content: "\f7fa";
}
.bxs-arrow-to-right-stroke:before {
    content: "\f7fb";
}
.bxs-arrow-to-top-stroke:before {
    content: "\f7fc";
}
.bxs-arrow-up-circle:before {
    content: "\f7fd";
}
.bxs-arrow-up-left-circle:before {
    content: "\f7fe";
}
.bxs-arrow-up-left-square:before {
    content: "\f7ff";
}
.bxs-arrow-up-left-stroke-circle:before {
    content: "\f800";
}
.bxs-arrow-up-left-stroke-square:before {
    content: "\f801";
}
.bxs-arrow-up-left-stroke:before {
    content: "\f802";
}
.bxs-arrow-up-left:before {
    content: "\f803";
}
.bxs-arrow-up-right-circle:before {
    content: "\f804";
}
.bxs-arrow-up-right-square:before {
    content: "\f805";
}
.bxs-arrow-up-right-stroke-circle:before {
    content: "\f806";
}
.bxs-arrow-up-right-stroke-square:before {
    content: "\f807";
}
.bxs-arrow-up-right-stroke:before {
    content: "\f808";
}
.bxs-arrow-up-right:before {
    content: "\f809";
}
.bxs-arrow-up-square:before {
    content: "\f80a";
}
.bxs-arrow-up-stroke-circle:before {
    content: "\f80b";
}
.bxs-arrow-up-stroke-square:before {
    content: "\f80c";
}
.bxs-arrow-up-stroke:before {
    content: "\f80d";
}
.bxs-arrow-up:before {
    content: "\f80e";
}
.bxs-article:before {
    content: "\f80f";
}
.bxs-asterisk:before {
    content: "\f810";
}
.bxs-at:before {
    content: "\f811";
}
.bxs-avocado:before {
    content: "\f812";
}
.bxs-axe:before {
    content: "\f813";
}
.bxs-background-color-fill:before {
    content: "\f814";
}
.bxs-background:before {
    content: "\f815";
}
.bxs-backpack-star:before {
    content: "\f816";
}
.bxs-backpack:before {
    content: "\f817";
}
.bxs-backspace:before {
    content: "\f818";
}
.bxs-badge-check:before {
    content: "\f819";
}
.bxs-badge-exclamation:before {
    content: "\f81a";
}
.bxs-badge-info:before {
    content: "\f81b";
}
.bxs-badge:before {
    content: "\f81c";
}
.bxs-baguette:before {
    content: "\f81d";
}
.bxs-balcony:before {
    content: "\f81e";
}
.bxs-ball-throw:before {
    content: "\f81f";
}
.bxs-balloon:before {
    content: "\f820";
}
.bxs-band-aid:before {
    content: "\f821";
}
.bxs-bank:before {
    content: "\f822";
}
.bxs-bar-chart-big:before {
    content: "\f823";
}
.bxs-bar-chart-square:before {
    content: "\f824";
}
.bxs-bar-chart:before {
    content: "\f825";
}
.bxs-barcode-square:before {
    content: "\f826";
}
.bxs-barcode:before {
    content: "\f827";
}
.bxs-barn:before {
    content: "\f828";
}
.bxs-baseball:before {
    content: "\f829";
}
.bxs-basket:before {
    content: "\f82a";
}
.bxs-basketball:before {
    content: "\f82b";
}
.bxs-bath:before {
    content: "\f82c";
}
.bxs-battery-1:before {
    content: "\f82d";
}
.bxs-battery-2:before {
    content: "\f82e";
}
.bxs-battery-3:before {
    content: "\f82f";
}
.bxs-battery-full:before {
    content: "\f830";
}
.bxs-battery-low:before {
    content: "\f831";
}
.bxs-battery:before {
    content: "\f832";
}
.bxs-beach-ball:before {
    content: "\f833";
}
.bxs-beach:before {
    content: "\f834";
}
.bxs-beaker:before {
    content: "\f835";
}
.bxs-beanie:before {
    content: "\f836";
}
.bxs-bear:before {
    content: "\f837";
}
.bxs-bed-alt:before {
    content: "\f838";
}
.bxs-bed:before {
    content: "\f839";
}
.bxs-beer:before {
    content: "\f83a";
}
.bxs-bell-check:before {
    content: "\f83b";
}
.bxs-bell-minus:before {
    content: "\f83c";
}
.bxs-bell-plus:before {
    content: "\f83d";
}
.bxs-bell-ring:before {
    content: "\f83e";
}
.bxs-bell-slash:before {
    content: "\f83f";
}
.bxs-bell:before {
    content: "\f840";
}
.bxs-bench:before {
    content: "\f841";
}
.bxs-bible:before {
    content: "\f842";
}
.bxs-binocular:before {
    content: "\f843";
}
.bxs-bird-alt:before {
    content: "\f844";
}
.bxs-bird:before {
    content: "\f845";
}
.bxs-birthday-cake:before {
    content: "\f846";
}
.bxs-bitcoin:before {
    content: "\f847";
}
.bxs-blanket:before {
    content: "\f848";
}
.bxs-block:before {
    content: "\f849";
}
.bxs-blockquote:before {
    content: "\f84a";
}
.bxs-blocks:before {
    content: "\f84b";
}
.bxs-bluetooth:before {
    content: "\f84c";
}
.bxs-blur:before {
    content: "\f84d";
}
.bxs-body:before {
    content: "\f84e";
}
.bxs-bold:before {
    content: "\f84f";
}
.bxs-bolt-circle:before {
    content: "\f850";
}
.bxs-bolt-square:before {
    content: "\f851";
}
.bxs-bolt:before {
    content: "\f852";
}
.bxs-bomb:before {
    content: "\f853";
}
.bxs-bone:before {
    content: "\f854";
}
.bxs-bong:before {
    content: "\f855";
}
.bxs-book-add:before {
    content: "\f856";
}
.bxs-book-alt:before {
    content: "\f857";
}
.bxs-book-bookmark:before {
    content: "\f858";
}
.bxs-book-content:before {
    content: "\f859";
}
.bxs-book-heart:before {
    content: "\f85a";
}
.bxs-book-library:before {
    content: "\f85b";
}
.bxs-book-open:before {
    content: "\f85c";
}
.bxs-book:before {
    content: "\f85d";
}
.bxs-bookmark-alt:before {
    content: "\f85e";
}
.bxs-bookmark-heart:before {
    content: "\f85f";
}
.bxs-bookmark-minus-alt:before {
    content: "\f860";
}
.bxs-bookmark-minus:before {
    content: "\f861";
}
.bxs-bookmark-plus-alt:before {
    content: "\f862";
}
.bxs-bookmark-plus:before {
    content: "\f863";
}
.bxs-bookmark-star:before {
    content: "\f864";
}
.bxs-bookmark:before {
    content: "\f865";
}
.bxs-bookmarks:before {
    content: "\f866";
}
.bxs-boombox:before {
    content: "\f867";
}
.bxs-boot:before {
    content: "\f868";
}
.bxs-border-all:before {
    content: "\f869";
}
.bxs-border-bottom:before {
    content: "\f86a";
}
.bxs-border-inner:before {
    content: "\f86b";
}
.bxs-border-left:before {
    content: "\f86c";
}
.bxs-border-none:before {
    content: "\f86d";
}
.bxs-border-outer:before {
    content: "\f86e";
}
.bxs-border-radius:before {
    content: "\f86f";
}
.bxs-border-right:before {
    content: "\f870";
}
.bxs-border-top:before {
    content: "\f871";
}
.bxs-bow:before {
    content: "\f872";
}
.bxs-bowl-balls:before {
    content: "\f873";
}
.bxs-bowl-bubbles:before {
    content: "\f874";
}
.bxs-bowl-hot:before {
    content: "\f875";
}
.bxs-bowl-noodles-alt:before {
    content: "\f876";
}
.bxs-bowl-noodles:before {
    content: "\f877";
}
.bxs-bowl-rice:before {
    content: "\f878";
}
.bxs-bowling-ball:before {
    content: "\f879";
}
.bxs-box-alt:before {
    content: "\f87a";
}
.bxs-box:before {
    content: "\f87b";
}
.bxs-bracket-curly:before {
    content: "\f87c";
}
.bxs-bracket-round:before {
    content: "\f87d";
}
.bxs-bracket:before {
    content: "\f87e";
}
.bxs-braille:before {
    content: "\f87f";
}
.bxs-brain:before {
    content: "\f880";
}
.bxs-bread:before {
    content: "\f881";
}
.bxs-brick:before {
    content: "\f882";
}
.bxs-bridge:before {
    content: "\f883";
}
.bxs-briefcase-alt-2:before {
    content: "\f884";
}
.bxs-briefcase-alt:before {
    content: "\f885";
}
.bxs-briefcase:before {
    content: "\f886";
}
.bxs-brightness-half:before {
    content: "\f887";
}
.bxs-brightness:before {
    content: "\f888";
}
.bxs-broadcast:before {
    content: "\f889";
}
.bxs-browser-activity:before {
    content: "\f88a";
}
.bxs-brush:before {
    content: "\f88b";
}
.bxs-bug-alt:before {
    content: "\f88c";
}
.bxs-bug:before {
    content: "\f88d";
}
.bxs-building-house:before {
    content: "\f88e";
}
.bxs-building:before {
    content: "\f88f";
}
.bxs-buildings:before {
    content: "\f890";
}
.bxs-bullseye:before {
    content: "\f891";
}
.bxs-buoy:before {
    content: "\f892";
}
.bxs-burger-alt:before {
    content: "\f893";
}
.bxs-burger:before {
    content: "\f894";
}
.bxs-bus:before {
    content: "\f895";
}
.bxs-business:before {
    content: "\f896";
}
.bxs-button-rounded:before {
    content: "\f897";
}
.bxs-button:before {
    content: "\f898";
}
.bxs-cabinet:before {
    content: "\f899";
}
.bxs-cable-car:before {
    content: "\f89a";
}
.bxs-cake-slice:before {
    content: "\f89b";
}
.bxs-calculator:before {
    content: "\f89c";
}
.bxs-calendar-alt-2:before {
    content: "\f89d";
}
.bxs-calendar-alt:before {
    content: "\f89e";
}
.bxs-calendar-check:before {
    content: "\f89f";
}
.bxs-calendar-cog:before {
    content: "\f8a0";
}
.bxs-calendar-detail:before {
    content: "\f8a1";
}
.bxs-calendar-down-arrow:before {
    content: "\f8a2";
}
.bxs-calendar-event:before {
    content: "\f8a3";
}
.bxs-calendar-heart:before {
    content: "\f8a4";
}
.bxs-calendar-minus:before {
    content: "\f8a5";
}
.bxs-calendar-plus:before {
    content: "\f8a6";
}
.bxs-calendar-search:before {
    content: "\f8a7";
}
.bxs-calendar-star:before {
    content: "\f8a8";
}
.bxs-calendar-up-arrow:before {
    content: "\f8a9";
}
.bxs-calendar-week:before {
    content: "\f8aa";
}
.bxs-calendar-x:before {
    content: "\f8ab";
}
.bxs-calendar:before {
    content: "\f8ac";
}
.bxs-camcoder:before {
    content: "\f8ad";
}
.bxs-camera-alt:before {
    content: "\f8ae";
}
.bxs-camera-flip:before {
    content: "\f8af";
}
.bxs-camera-home:before {
    content: "\f8b0";
}
.bxs-camera-monochrome:before {
    content: "\f8b1";
}
.bxs-camera-plus:before {
    content: "\f8b2";
}
.bxs-camera-portrait:before {
    content: "\f8b3";
}
.bxs-camera-slash:before {
    content: "\f8b4";
}
.bxs-camera-switch:before {
    content: "\f8b5";
}
.bxs-camera:before {
    content: "\f8b6";
}
.bxs-campfire:before {
    content: "\f8b7";
}
.bxs-camping:before {
    content: "\f8b8";
}
.bxs-candlestick:before {
    content: "\f8b9";
}
.bxs-cannabis:before {
    content: "\f8ba";
}
.bxs-cap:before {
    content: "\f8bb";
}
.bxs-capitalize:before {
    content: "\f8bc";
}
.bxs-capsule:before {
    content: "\f8bd";
}
.bxs-captions-cc:before {
    content: "\f8be";
}
.bxs-captions:before {
    content: "\f8bf";
}
.bxs-capture:before {
    content: "\f8c0";
}
.bxs-car-battery:before {
    content: "\f8c1";
}
.bxs-car-key:before {
    content: "\f8c2";
}
.bxs-car:before {
    content: "\f8c3";
}
.bxs-card-view-large:before {
    content: "\f8c4";
}
.bxs-card-view-no-title:before {
    content: "\f8c5";
}
.bxs-card-view-small:before {
    content: "\f8c6";
}
.bxs-card-view-tiles:before {
    content: "\f8c7";
}
.bxs-card-view:before {
    content: "\f8c8";
}
.bxs-caret-down-circle:before {
    content: "\f8c9";
}
.bxs-caret-down-square:before {
    content: "\f8ca";
}
.bxs-caret-down:before {
    content: "\f8cb";
}
.bxs-caret-left-circle:before {
    content: "\f8cc";
}
.bxs-caret-left-square:before {
    content: "\f8cd";
}
.bxs-caret-left:before {
    content: "\f8ce";
}
.bxs-caret-right-circle:before {
    content: "\f8cf";
}
.bxs-caret-right-square:before {
    content: "\f8d0";
}
.bxs-caret-right:before {
    content: "\f8d1";
}
.bxs-caret-up-circle:before {
    content: "\f8d2";
}
.bxs-caret-up-square:before {
    content: "\f8d3";
}
.bxs-caret-up:before {
    content: "\f8d4";
}
.bxs-carets-down-up:before {
    content: "\f8d5";
}
.bxs-carets-left-right:before {
    content: "\f8d6";
}
.bxs-carets-right-left:before {
    content: "\f8d7";
}
.bxs-carets-up-down:before {
    content: "\f8d8";
}
.bxs-carrot:before {
    content: "\f8d9";
}
.bxs-cart-minus:before {
    content: "\f8da";
}
.bxs-cart-plus:before {
    content: "\f8db";
}
.bxs-cart:before {
    content: "\f8dc";
}
.bxs-cast:before {
    content: "\f8dd";
}
.bxs-castle:before {
    content: "\f8de";
}
.bxs-cat:before {
    content: "\f8df";
}
.bxs-categories:before {
    content: "\f8e0";
}
.bxs-cctv:before {
    content: "\f8e1";
}
.bxs-certification:before {
    content: "\f8e2";
}
.bxs-chair:before {
    content: "\f8e3";
}
.bxs-champagne:before {
    content: "\f8e4";
}
.bxs-chart-area:before {
    content: "\f8e5";
}
.bxs-chart-bar-big-columns:before {
    content: "\f8e6";
}
.bxs-chart-bar-big-rows:before {
    content: "\f8e7";
}
.bxs-chart-bar-columns:before {
    content: "\f8e8";
}
.bxs-chart-bar-rows:before {
    content: "\f8e9";
}
.bxs-chart-bubble:before {
    content: "\f8ea";
}
.bxs-chart-gantt:before {
    content: "\f8eb";
}
.bxs-chart-line:before {
    content: "\f8ec";
}
.bxs-chart-network:before {
    content: "\f8ed";
}
.bxs-chart-scatter:before {
    content: "\f8ee";
}
.bxs-chart-spline:before {
    content: "\f8ef";
}
.bxs-chart-stacked-columns:before {
    content: "\f8f0";
}
.bxs-chart-stacked-rows:before {
    content: "\f8f1";
}
.bxs-chart-trend:before {
    content: "\f8f2";
}
.bxs-check-circle:before {
    content: "\f8f3";
}
.bxs-check-square:before {
    content: "\f8f4";
}
.bxs-check:before {
    content: "\f8f5";
}
.bxs-checkbox-checked:before {
    content: "\f8f6";
}
.bxs-checkbox-square:before {
    content: "\f8f7";
}
.bxs-checkbox:before {
    content: "\f8f8";
}
.bxs-checklist:before {
    content: "\f8f9";
}
.bxs-checks:before {
    content: "\f8fa";
}
.bxs-cheese:before {
    content: "\f8fb";
}
.bxs-chef-hat:before {
    content: "\f8fc";
}
.bxs-cherry:before {
    content: "\f8fd";
}
.bxs-chess-bishop:before {
    content: "\f8fe";
}
.bxs-chess-king:before {
    content: "\f8ff";
}
.bxs-chess-knight:before {
    content: "\f900";
}
.bxs-chess-pawn:before {
    content: "\f901";
}
.bxs-chess-queen:before {
    content: "\f902";
}
.bxs-chess-rook:before {
    content: "\f903";
}
.bxs-chess:before {
    content: "\f904";
}
.bxs-chevron-down-circle:before {
    content: "\f905";
}
.bxs-chevron-down-square:before {
    content: "\f906";
}
.bxs-chevron-down:before {
    content: "\f907";
}
.bxs-chevron-left-circle:before {
    content: "\f908";
}
.bxs-chevron-left-square:before {
    content: "\f909";
}
.bxs-chevron-left:before {
    content: "\f90a";
}
.bxs-chevron-right-circle:before {
    content: "\f90b";
}
.bxs-chevron-right-square:before {
    content: "\f90c";
}
.bxs-chevron-right:before {
    content: "\f90d";
}
.bxs-chevron-up-circle:before {
    content: "\f90e";
}
.bxs-chevron-up-square:before {
    content: "\f90f";
}
.bxs-chevron-up:before {
    content: "\f910";
}
.bxs-chevrons-down-up:before {
    content: "\f911";
}
.bxs-chevrons-down:before {
    content: "\f912";
}
.bxs-chevrons-left-right:before {
    content: "\f913";
}
.bxs-chevrons-left:before {
    content: "\f914";
}
.bxs-chevrons-right-left:before {
    content: "\f915";
}
.bxs-chevrons-right:before {
    content: "\f916";
}
.bxs-chevrons-up-down:before {
    content: "\f917";
}
.bxs-chevrons-up:before {
    content: "\f918";
}
.bxs-child:before {
    content: "\f919";
}
.bxs-chip:before {
    content: "\f91a";
}
.bxs-church:before {
    content: "\f91b";
}
.bxs-cigarette:before {
    content: "\f91c";
}
.bxs-circle-dashed-half:before {
    content: "\f91d";
}
.bxs-circle-dashed:before {
    content: "\f91e";
}
.bxs-circle-half-alt:before {
    content: "\f91f";
}
.bxs-circle-half:before {
    content: "\f920";
}
.bxs-circle-hexagon:before {
    content: "\f921";
}
.bxs-circle-outer-dashed-circle:before {
    content: "\f922";
}
.bxs-circle-quarter-alt:before {
    content: "\f923";
}
.bxs-circle-quarter:before {
    content: "\f924";
}
.bxs-circle-three-quarter-alt:before {
    content: "\f925";
}
.bxs-circle-three-quarter:before {
    content: "\f926";
}
.bxs-circle:before {
    content: "\f927";
}
.bxs-city:before {
    content: "\f928";
}
.bxs-clipboard-check:before {
    content: "\f929";
}
.bxs-clipboard-code:before {
    content: "\f92a";
}
.bxs-clipboard-detail:before {
    content: "\f92b";
}
.bxs-clipboard-minus:before {
    content: "\f92c";
}
.bxs-clipboard-plus:before {
    content: "\f92d";
}
.bxs-clipboard-x:before {
    content: "\f92e";
}
.bxs-clipboard:before {
    content: "\f92f";
}
.bxs-clock-1:before {
    content: "\f930";
}
.bxs-clock-10:before {
    content: "\f931";
}
.bxs-clock-11:before {
    content: "\f932";
}
.bxs-clock-12:before {
    content: "\f933";
}
.bxs-clock-2:before {
    content: "\f934";
}
.bxs-clock-3:before {
    content: "\f935";
}
.bxs-clock-4:before {
    content: "\f936";
}
.bxs-clock-5:before {
    content: "\f937";
}
.bxs-clock-6:before {
    content: "\f938";
}
.bxs-clock-7:before {
    content: "\f939";
}
.bxs-clock-8:before {
    content: "\f93a";
}
.bxs-clock-9:before {
    content: "\f93b";
}
.bxs-cloud-alt:before {
    content: "\f93c";
}
.bxs-cloud-drizzle:before {
    content: "\f93d";
}
.bxs-cloud-fog:before {
    content: "\f93e";
}
.bxs-cloud-lightning:before {
    content: "\f93f";
}
.bxs-cloud-moon:before {
    content: "\f940";
}
.bxs-cloud-rain-wind:before {
    content: "\f941";
}
.bxs-cloud-rain:before {
    content: "\f942";
}
.bxs-cloud-snow:before {
    content: "\f943";
}
.bxs-cloud-sun:before {
    content: "\f944";
}
.bxs-cloud:before {
    content: "\f945";
}
.bxs-club:before {
    content: "\f946";
}
.bxs-cocktail:before {
    content: "\f947";
}
.bxs-code-alt:before {
    content: "\f948";
}
.bxs-code:before {
    content: "\f949";
}
.bxs-coffee-beans:before {
    content: "\f94a";
}
.bxs-coffee-cup:before {
    content: "\f94b";
}
.bxs-coffee:before {
    content: "\f94c";
}
.bxs-cog:before {
    content: "\f94d";
}
.bxs-cognition:before {
    content: "\f94e";
}
.bxs-coin:before {
    content: "\f94f";
}
.bxs-coins:before {
    content: "\f950";
}
.bxs-col-resize:before {
    content: "\f951";
}
.bxs-color-fill:before {
    content: "\f952";
}
.bxs-color-wheel:before {
    content: "\f953";
}
.bxs-columns-3:before {
    content: "\f954";
}
.bxs-columns-4:before {
    content: "\f955";
}
.bxs-columns:before {
    content: "\f956";
}
.bxs-command:before {
    content: "\f957";
}
.bxs-community:before {
    content: "\f958";
}
.bxs-compare-alt:before {
    content: "\f959";
}
.bxs-compare:before {
    content: "\f95a";
}
.bxs-compass:before {
    content: "\f95b";
}
.bxs-component:before {
    content: "\f95c";
}
.bxs-computer:before {
    content: "\f95d";
}
.bxs-confused:before {
    content: "\f95e";
}
.bxs-connector:before {
    content: "\f95f";
}
.bxs-contact-book:before {
    content: "\f960";
}
.bxs-contrast:before {
    content: "\f961";
}
.bxs-cookie:before {
    content: "\f962";
}
.bxs-cool:before {
    content: "\f963";
}
.bxs-copy-check:before {
    content: "\f964";
}
.bxs-copy-list:before {
    content: "\f965";
}
.bxs-copy-minus:before {
    content: "\f966";
}
.bxs-copy-plus:before {
    content: "\f967";
}
.bxs-copy-x:before {
    content: "\f968";
}
.bxs-copy:before {
    content: "\f969";
}
.bxs-copyright:before {
    content: "\f96a";
}
.bxs-credit-card-alt:before {
    content: "\f96b";
}
.bxs-credit-card-front:before {
    content: "\f96c";
}
.bxs-credit-card-insert:before {
    content: "\f96d";
}
.bxs-credit-card:before {
    content: "\f96e";
}
.bxs-cricket-ball:before {
    content: "\f96f";
}
.bxs-crop:before {
    content: "\f970";
}
.bxs-crosshair:before {
    content: "\f971";
}
.bxs-crown:before {
    content: "\f972";
}
.bxs-cube-alt:before {
    content: "\f973";
}
.bxs-cube-inside:before {
    content: "\f974";
}
.bxs-cube:before {
    content: "\f975";
}
.bxs-cuboid:before {
    content: "\f976";
}
.bxs-cup-hot:before {
    content: "\f977";
}
.bxs-cup-saucer:before {
    content: "\f978";
}
.bxs-cup-tea:before {
    content: "\f979";
}
.bxs-cup:before {
    content: "\f97a";
}
.bxs-cupboard-alt:before {
    content: "\f97b";
}
.bxs-cupboard:before {
    content: "\f97c";
}
.bxs-cupcake:before {
    content: "\f97d";
}
.bxs-currency-note:before {
    content: "\f97e";
}
.bxs-currency-notes:before {
    content: "\f97f";
}
.bxs-cursor-add:before {
    content: "\f980";
}
.bxs-cursor-cell:before {
    content: "\f981";
}
.bxs-cursor-crosshair-dot:before {
    content: "\f982";
}
.bxs-cursor-crosshair:before {
    content: "\f983";
}
.bxs-cursor-pen:before {
    content: "\f984";
}
.bxs-cursor-pointer:before {
    content: "\f985";
}
.bxs-cursor:before {
    content: "\f986";
}
.bxs-cut:before {
    content: "\f987";
}
.bxs-cycling:before {
    content: "\f988";
}
.bxs-cylinder:before {
    content: "\f989";
}
.bxs-dashboard-alt:before {
    content: "\f98a";
}
.bxs-dashboard:before {
    content: "\f98b";
}
.bxs-database-alt:before {
    content: "\f98c";
}
.bxs-database:before {
    content: "\f98d";
}
.bxs-decrease-indent:before {
    content: "\f98e";
}
.bxs-department-store:before {
    content: "\f98f";
}
.bxs-desert:before {
    content: "\f990";
}
.bxs-desk:before {
    content: "\f991";
}
.bxs-desktop-alt:before {
    content: "\f992";
}
.bxs-desktop:before {
    content: "\f993";
}
.bxs-devices:before {
    content: "\f994";
}
.bxs-dialpad:before {
    content: "\f995";
}
.bxs-diameter:before {
    content: "\f996";
}
.bxs-diamond-alt:before {
    content: "\f997";
}
.bxs-diamond:before {
    content: "\f998";
}
.bxs-diamonds:before {
    content: "\f999";
}
.bxs-dice-1:before {
    content: "\f99a";
}
.bxs-dice-2:before {
    content: "\f99b";
}
.bxs-dice-3:before {
    content: "\f99c";
}
.bxs-dice-4:before {
    content: "\f99d";
}
.bxs-dice-5:before {
    content: "\f99e";
}
.bxs-dice-6:before {
    content: "\f99f";
}
.bxs-dice-roll:before {
    content: "\f9a0";
}
.bxs-dino:before {
    content: "\f9a1";
}
.bxs-directions:before {
    content: "\f9a2";
}
.bxs-disc:before {
    content: "\f9a3";
}
.bxs-discount:before {
    content: "\f9a4";
}
.bxs-discussion:before {
    content: "\f9a5";
}
.bxs-dish:before {
    content: "\f9a6";
}
.bxs-dishwasher:before {
    content: "\f9a7";
}
.bxs-dislike:before {
    content: "\f9a8";
}
.bxs-dizzy:before {
    content: "\f9a9";
}
.bxs-dna:before {
    content: "\f9aa";
}
.bxs-dock-bottom-alt:before {
    content: "\f9ab";
}
.bxs-dock-bottom-arrow:before {
    content: "\f9ac";
}
.bxs-dock-bottom-left-alt:before {
    content: "\f9ad";
}
.bxs-dock-bottom-left:before {
    content: "\f9ae";
}
.bxs-dock-bottom-right-alt:before {
    content: "\f9af";
}
.bxs-dock-bottom-right:before {
    content: "\f9b0";
}
.bxs-dock-bottom:before {
    content: "\f9b1";
}
.bxs-dock-left-alt:before {
    content: "\f9b2";
}
.bxs-dock-left-arrow:before {
    content: "\f9b3";
}
.bxs-dock-left:before {
    content: "\f9b4";
}
.bxs-dock-right-alt:before {
    content: "\f9b5";
}
.bxs-dock-right-arrow:before {
    content: "\f9b6";
}
.bxs-dock-right:before {
    content: "\f9b7";
}
.bxs-dock-top-alt:before {
    content: "\f9b8";
}
.bxs-dock-top-arrow:before {
    content: "\f9b9";
}
.bxs-dock-top-left-alt:before {
    content: "\f9ba";
}
.bxs-dock-top-left:before {
    content: "\f9bb";
}
.bxs-dock-top-right-alt:before {
    content: "\f9bc";
}
.bxs-dock-top-right:before {
    content: "\f9bd";
}
.bxs-dock-top:before {
    content: "\f9be";
}
.bxs-dog-alt:before {
    content: "\f9bf";
}
.bxs-dog:before {
    content: "\f9c0";
}
.bxs-dollar-circle:before {
    content: "\f9c1";
}
.bxs-dollar:before {
    content: "\f9c2";
}
.bxs-donate-blood:before {
    content: "\f9c3";
}
.bxs-donate-heart:before {
    content: "\f9c4";
}
.bxs-donut:before {
    content: "\f9c5";
}
.bxs-door-open:before {
    content: "\f9c6";
}
.bxs-door:before {
    content: "\f9c7";
}
.bxs-dots-horizontal-rounded-circle:before {
    content: "\f9c8";
}
.bxs-dots-horizontal-rounded:before {
    content: "\f9c9";
}
.bxs-dots-horizontal:before {
    content: "\f9ca";
}
.bxs-dots-vertical-rounded-circle:before {
    content: "\f9cb";
}
.bxs-dots-vertical-rounded:before {
    content: "\f9cc";
}
.bxs-dots-vertical:before {
    content: "\f9cd";
}
.bxs-doughnut-chart:before {
    content: "\f9ce";
}
.bxs-draw-ahead:before {
    content: "\f9cf";
}
.bxs-draw-behind:before {
    content: "\f9d0";
}
.bxs-draw-inside:before {
    content: "\f9d1";
}
.bxs-dress:before {
    content: "\f9d2";
}
.bxs-dribbling:before {
    content: "\f9d3";
}
.bxs-dropdown:before {
    content: "\f9d4";
}
.bxs-dryer:before {
    content: "\f9d5";
}
.bxs-dumbbell-alt:before {
    content: "\f9d6";
}
.bxs-dumbbell:before {
    content: "\f9d7";
}
.bxs-ear-alt:before {
    content: "\f9d8";
}
.bxs-ear-slash:before {
    content: "\f9d9";
}
.bxs-ear:before {
    content: "\f9da";
}
.bxs-earbuds:before {
    content: "\f9db";
}
.bxs-ease-in-out:before {
    content: "\f9dc";
}
.bxs-ease-in:before {
    content: "\f9dd";
}
.bxs-ease-out:before {
    content: "\f9de";
}
.bxs-edit-alt:before {
    content: "\f9df";
}
.bxs-edit:before {
    content: "\f9e0";
}
.bxs-education:before {
    content: "\f9e1";
}
.bxs-egg-fried:before {
    content: "\f9e2";
}
.bxs-egg-yolk:before {
    content: "\f9e3";
}
.bxs-egg:before {
    content: "\f9e4";
}
.bxs-eject:before {
    content: "\f9e5";
}
.bxs-enterprise:before {
    content: "\f9e6";
}
.bxs-envelope-alt:before {
    content: "\f9e7";
}
.bxs-envelope-open:before {
    content: "\f9e8";
}
.bxs-envelope:before {
    content: "\f9e9";
}
.bxs-equal-circle:before {
    content: "\f9ea";
}
.bxs-equal-square:before {
    content: "\f9eb";
}
.bxs-equal:before {
    content: "\f9ec";
}
.bxs-equalizer:before {
    content: "\f9ed";
}
.bxs-eraser:before {
    content: "\f9ee";
}
.bxs-euro:before {
    content: "\f9ef";
}
.bxs-ev-station:before {
    content: "\f9f0";
}
.bxs-expand-left:before {
    content: "\f9f1";
}
.bxs-expand-right:before {
    content: "\f9f2";
}
.bxs-exposure:before {
    content: "\f9f3";
}
.bxs-extension:before {
    content: "\f9f4";
}
.bxs-eye-alt:before {
    content: "\f9f5";
}
.bxs-eye-big:before {
    content: "\f9f6";
}
.bxs-eye-closed:before {
    content: "\f9f7";
}
.bxs-eye-slash:before {
    content: "\f9f8";
}
.bxs-eye:before {
    content: "\f9f9";
}
.bxs-eyedropper:before {
    content: "\f9fa";
}
.bxs-face-alt-2:before {
    content: "\f9fb";
}
.bxs-face-alt-3:before {
    content: "\f9fc";
}
.bxs-face-alt-4:before {
    content: "\f9fd";
}
.bxs-face-alt:before {
    content: "\f9fe";
}
.bxs-face-child:before {
    content: "\f9ff";
}
.bxs-face-mask:before {
    content: "\fa00";
}
.bxs-face:before {
    content: "\fa01";
}
.bxs-factory:before {
    content: "\fa02";
}
.bxs-fast-forward-circle:before {
    content: "\fa03";
}
.bxs-fast-forward:before {
    content: "\fa04";
}
.bxs-female:before {
    content: "\fa05";
}
.bxs-file-code:before {
    content: "\fa06";
}
.bxs-file-cog:before {
    content: "\fa07";
}
.bxs-file-detail:before {
    content: "\fa08";
}
.bxs-file-heart:before {
    content: "\fa09";
}
.bxs-file-minus:before {
    content: "\fa0a";
}
.bxs-file-plus:before {
    content: "\fa0b";
}
.bxs-file-report:before {
    content: "\fa0c";
}
.bxs-file-search:before {
    content: "\fa0d";
}
.bxs-file-star:before {
    content: "\fa0e";
}
.bxs-file-x:before {
    content: "\fa0f";
}
.bxs-file-zip:before {
    content: "\fa10";
}
.bxs-file:before {
    content: "\fa11";
}
.bxs-film-roll-alt:before {
    content: "\fa12";
}
.bxs-film-roll:before {
    content: "\fa13";
}
.bxs-film:before {
    content: "\fa14";
}
.bxs-filter:before {
    content: "\fa15";
}
.bxs-finger-down:before {
    content: "\fa16";
}
.bxs-finger-left:before {
    content: "\fa17";
}
.bxs-finger-right:before {
    content: "\fa18";
}
.bxs-finger-swipe-down:before {
    content: "\fa19";
}
.bxs-finger-swipe-left:before {
    content: "\fa1a";
}
.bxs-finger-swipe-right:before {
    content: "\fa1b";
}
.bxs-finger-swipe-up:before {
    content: "\fa1c";
}
.bxs-finger-up:before {
    content: "\fa1d";
}
.bxs-fingerprint:before {
    content: "\fa1e";
}
.bxs-fire-alt:before {
    content: "\fa1f";
}
.bxs-fire-extinguisher:before {
    content: "\fa20";
}
.bxs-fire:before {
    content: "\fa21";
}
.bxs-first:before {
    content: "\fa22";
}
.bxs-fish-alt:before {
    content: "\fa23";
}
.bxs-fish:before {
    content: "\fa24";
}
.bxs-flag-alt-2:before {
    content: "\fa25";
}
.bxs-flag-alt-3:before {
    content: "\fa26";
}
.bxs-flag-alt:before {
    content: "\fa27";
}
.bxs-flag-chequered:before {
    content: "\fa28";
}
.bxs-flag:before {
    content: "\fa29";
}
.bxs-flame:before {
    content: "\fa2a";
}
.bxs-flask-round:before {
    content: "\fa2b";
}
.bxs-florist:before {
    content: "\fa2c";
}
.bxs-flower-alt-2:before {
    content: "\fa2d";
}
.bxs-flower-alt:before {
    content: "\fa2e";
}
.bxs-flower:before {
    content: "\fa2f";
}
.bxs-folder-check:before {
    content: "\fa30";
}
.bxs-folder-code:before {
    content: "\fa31";
}
.bxs-folder-cog:before {
    content: "\fa32";
}
.bxs-folder-down-arrow:before {
    content: "\fa33";
}
.bxs-folder-heart:before {
    content: "\fa34";
}
.bxs-folder-minus:before {
    content: "\fa35";
}
.bxs-folder-open:before {
    content: "\fa36";
}
.bxs-folder-plus:before {
    content: "\fa37";
}
.bxs-folder-search:before {
    content: "\fa38";
}
.bxs-folder-star:before {
    content: "\fa39";
}
.bxs-folder-up-arrow:before {
    content: "\fa3a";
}
.bxs-folder-x:before {
    content: "\fa3b";
}
.bxs-folder-zip:before {
    content: "\fa3c";
}
.bxs-folder:before {
    content: "\fa3d";
}
.bxs-font-color:before {
    content: "\fa3e";
}
.bxs-font-family:before {
    content: "\fa3f";
}
.bxs-food-menu:before {
    content: "\fa40";
}
.bxs-food-tag:before {
    content: "\fa41";
}
.bxs-football-kick:before {
    content: "\fa42";
}
.bxs-football-pitch:before {
    content: "\fa43";
}
.bxs-football:before {
    content: "\fa44";
}
.bxs-footsteps:before {
    content: "\fa45";
}
.bxs-foreground:before {
    content: "\fa46";
}
.bxs-fork-knife:before {
    content: "\fa47";
}
.bxs-fork-spoon:before {
    content: "\fa48";
}
.bxs-fork:before {
    content: "\fa49";
}
.bxs-form:before {
    content: "\fa4a";
}
.bxs-forward-big:before {
    content: "\fa4b";
}
.bxs-forward-stroke:before {
    content: "\fa4c";
}
.bxs-forward:before {
    content: "\fa4d";
}
.bxs-frame:before {
    content: "\fa4e";
}
.bxs-fridge:before {
    content: "\fa4f";
}
.bxs-fullscreen-exit:before {
    content: "\fa50";
}
.bxs-fullscreen:before {
    content: "\fa51";
}
.bxs-functions:before {
    content: "\fa52";
}
.bxs-future:before {
    content: "\fa53";
}
.bxs-gallery-horizontal-end:before {
    content: "\fa54";
}
.bxs-gallery-horizontal:before {
    content: "\fa55";
}
.bxs-gallery-thumbnails:before {
    content: "\fa56";
}
.bxs-gallery-vertical-end:before {
    content: "\fa57";
}
.bxs-gallery-vertical:before {
    content: "\fa58";
}
.bxs-gaming:before {
    content: "\fa59";
}
.bxs-garage:before {
    content: "\fa5a";
}
.bxs-gavel:before {
    content: "\fa5b";
}
.bxs-gear:before {
    content: "\fa5c";
}
.bxs-gem:before {
    content: "\fa5d";
}
.bxs-gestures:before {
    content: "\fa5e";
}
.bxs-ghost:before {
    content: "\fa5f";
}
.bxs-gift:before {
    content: "\fa60";
}
.bxs-git-branch:before {
    content: "\fa61";
}
.bxs-git-commit:before {
    content: "\fa62";
}
.bxs-git-compare:before {
    content: "\fa63";
}
.bxs-git-merge-queue:before {
    content: "\fa64";
}
.bxs-git-merge:before {
    content: "\fa65";
}
.bxs-git-pull-request-closed:before {
    content: "\fa66";
}
.bxs-git-pull-request-draft:before {
    content: "\fa67";
}
.bxs-git-pull-request:before {
    content: "\fa68";
}
.bxs-git-repo-forked:before {
    content: "\fa69";
}
.bxs-glasses-alt:before {
    content: "\fa6a";
}
.bxs-glasses:before {
    content: "\fa6b";
}
.bxs-globe-africa:before {
    content: "\fa6c";
}
.bxs-globe-alt:before {
    content: "\fa6d";
}
.bxs-globe-americas:before {
    content: "\fa6e";
}
.bxs-globe-antartica:before {
    content: "\fa6f";
}
.bxs-globe-asia:before {
    content: "\fa70";
}
.bxs-globe-europe:before {
    content: "\fa71";
}
.bxs-globe-oceania:before {
    content: "\fa72";
}
.bxs-globe-stand:before {
    content: "\fa73";
}
.bxs-globe:before {
    content: "\fa74";
}
.bxs-golf-ball:before {
    content: "\fa75";
}
.bxs-gradient:before {
    content: "\fa76";
}
.bxs-grid-9:before {
    content: "\fa77";
}
.bxs-grid-circle-diagonal-left:before {
    content: "\fa78";
}
.bxs-grid-circle-diagonal-right:before {
    content: "\fa79";
}
.bxs-grid-circle-plus:before {
    content: "\fa7a";
}
.bxs-grid-circle:before {
    content: "\fa7b";
}
.bxs-grid-column-left:before {
    content: "\fa7c";
}
.bxs-grid-column-right:before {
    content: "\fa7d";
}
.bxs-grid-plus:before {
    content: "\fa7e";
}
.bxs-grid-row-bottom:before {
    content: "\fa7f";
}
.bxs-grid-row-top:before {
    content: "\fa80";
}
.bxs-grid-search:before {
    content: "\fa81";
}
.bxs-grid:before {
    content: "\fa82";
}
.bxs-groceries:before {
    content: "\fa83";
}
.bxs-group:before {
    content: "\fa84";
}
.bxs-guitar-amp:before {
    content: "\fa85";
}
.bxs-hail:before {
    content: "\fa86";
}
.bxs-hand-rock:before {
    content: "\fa87";
}
.bxs-hand:before {
    content: "\fa88";
}
.bxs-handheld-alt-1:before {
    content: "\fa89";
}
.bxs-handheld-alt:before {
    content: "\fa8a";
}
.bxs-handheld:before {
    content: "\fa8b";
}
.bxs-handshake:before {
    content: "\fa8c";
}
.bxs-hanger:before {
    content: "\fa8d";
}
.bxs-happy-alt:before {
    content: "\fa8e";
}
.bxs-happy-beaming:before {
    content: "\fa8f";
}
.bxs-happy-heart-eyes:before {
    content: "\fa90";
}
.bxs-happy:before {
    content: "\fa91";
}
.bxs-hard-drive:before {
    content: "\fa92";
}
.bxs-hard-hat:before {
    content: "\fa93";
}
.bxs-hashtag:before {
    content: "\fa94";
}
.bxs-hdmi:before {
    content: "\fa95";
}
.bxs-head:before {
    content: "\fa96";
}
.bxs-heading-1:before {
    content: "\fa97";
}
.bxs-heading-2:before {
    content: "\fa98";
}
.bxs-heading-3:before {
    content: "\fa99";
}
.bxs-heading:before {
    content: "\fa9a";
}
.bxs-headphone-alt-2:before {
    content: "\fa9b";
}
.bxs-headphone-alt:before {
    content: "\fa9c";
}
.bxs-headphone-mic:before {
    content: "\fa9d";
}
.bxs-headphone:before {
    content: "\fa9e";
}
.bxs-heart-break:before {
    content: "\fa9f";
}
.bxs-heart-circle:before {
    content: "\faa0";
}
.bxs-heart-half:before {
    content: "\faa1";
}
.bxs-heart-plus:before {
    content: "\faa2";
}
.bxs-heart-square:before {
    content: "\faa3";
}
.bxs-heart:before {
    content: "\faa4";
}
.bxs-heat-wave:before {
    content: "\faa5";
}
.bxs-helmet:before {
    content: "\faa6";
}
.bxs-help-circle:before {
    content: "\faa7";
}
.bxs-help-octagon:before {
    content: "\faa8";
}
.bxs-hexagon:before {
    content: "\faa9";
}
.bxs-high-speed-train:before {
    content: "\faaa";
}
.bxs-highlight:before {
    content: "\faab";
}
.bxs-highlights:before {
    content: "\faac";
}
.bxs-history:before {
    content: "\faad";
}
.bxs-home-add:before {
    content: "\faae";
}
.bxs-home-alt-2:before {
    content: "\faaf";
}
.bxs-home-alt-3:before {
    content: "\fab0";
}
.bxs-home-alt:before {
    content: "\fab1";
}
.bxs-home-circle:before {
    content: "\fab2";
}
.bxs-home-heart:before {
    content: "\fab3";
}
.bxs-home:before {
    content: "\fab4";
}
.bxs-honey:before {
    content: "\fab5";
}
.bxs-horizon-sea:before {
    content: "\fab6";
}
.bxs-horizontal-align-center:before {
    content: "\fab7";
}
.bxs-horizontal-align-left:before {
    content: "\fab8";
}
.bxs-horizontal-align-right:before {
    content: "\fab9";
}
.bxs-horizontal-center:before {
    content: "\faba";
}
.bxs-horizontal-distribute-center:before {
    content: "\fabb";
}
.bxs-horizontal-distribute-left:before {
    content: "\fabc";
}
.bxs-horizontal-distribute-right:before {
    content: "\fabd";
}
.bxs-horizontal-left:before {
    content: "\fabe";
}
.bxs-horizontal-right:before {
    content: "\fabf";
}
.bxs-horizontal-spacing:before {
    content: "\fac0";
}
.bxs-hospital:before {
    content: "\fac1";
}
.bxs-hot-tub-water:before {
    content: "\fac2";
}
.bxs-hot-tub:before {
    content: "\fac3";
}
.bxs-hot:before {
    content: "\fac4";
}
.bxs-hourglass:before {
    content: "\fac5";
}
.bxs-hurricane:before {
    content: "\fac6";
}
.bxs-icecream:before {
    content: "\fac7";
}
.bxs-iframe:before {
    content: "\fac8";
}
.bxs-image-alt:before {
    content: "\fac9";
}
.bxs-image-circle:before {
    content: "\faca";
}
.bxs-image-landscape:before {
    content: "\facb";
}
.bxs-image-no-background:before {
    content: "\facc";
}
.bxs-image-plus:before {
    content: "\facd";
}
.bxs-image-portrait:before {
    content: "\face";
}
.bxs-image:before {
    content: "\facf";
}
.bxs-images:before {
    content: "\fad0";
}
.bxs-inbox:before {
    content: "\fad1";
}
.bxs-infinite:before {
    content: "\fad2";
}
.bxs-info-circle:before {
    content: "\fad3";
}
.bxs-info-octagon:before {
    content: "\fad4";
}
.bxs-info-shield:before {
    content: "\fad5";
}
.bxs-info-square:before {
    content: "\fad6";
}
.bxs-inner-shadow:before {
    content: "\fad7";
}
.bxs-institution:before {
    content: "\fad8";
}
.bxs-intellect:before {
    content: "\fad9";
}
.bxs-invert-adjust:before {
    content: "\fada";
}
.bxs-invert:before {
    content: "\fadb";
}
.bxs-island:before {
    content: "\fadc";
}
.bxs-italic:before {
    content: "\fadd";
}
.bxs-joystick-alt:before {
    content: "\fade";
}
.bxs-joystick-button:before {
    content: "\fadf";
}
.bxs-joystick:before {
    content: "\fae0";
}
.bxs-key-alt:before {
    content: "\fae1";
}
.bxs-key:before {
    content: "\fae2";
}
.bxs-keyboard:before {
    content: "\fae3";
}
.bxs-keyframe-ease-in:before {
    content: "\fae4";
}
.bxs-keyframe-ease-out:before {
    content: "\fae5";
}
.bxs-keyframe-easy-ease:before {
    content: "\fae6";
}
.bxs-keyframe-hold-ease-in:before {
    content: "\fae7";
}
.bxs-keyframe-hold-ease-out:before {
    content: "\fae8";
}
.bxs-keyframe-hold-linear-in:before {
    content: "\fae9";
}
.bxs-keyframe-hold-linear-out:before {
    content: "\faea";
}
.bxs-keyframe:before {
    content: "\faeb";
}
.bxs-knife:before {
    content: "\faec";
}
.bxs-landmark:before {
    content: "\faed";
}
.bxs-laptop-alt:before {
    content: "\faee";
}
.bxs-laptop:before {
    content: "\faef";
}
.bxs-lasso:before {
    content: "\faf0";
}
.bxs-last:before {
    content: "\faf1";
}
.bxs-laugh:before {
    content: "\faf2";
}
.bxs-law:before {
    content: "\faf3";
}
.bxs-layers-alt:before {
    content: "\faf4";
}
.bxs-layers-down-left:before {
    content: "\faf5";
}
.bxs-layers-down-right:before {
    content: "\faf6";
}
.bxs-layers-minus-alt:before {
    content: "\faf7";
}
.bxs-layers-plus-alt:before {
    content: "\faf8";
}
.bxs-layers:before {
    content: "\faf9";
}
.bxs-layout-check:before {
    content: "\fafa";
}
.bxs-layout-minus:before {
    content: "\fafb";
}
.bxs-layout-plus:before {
    content: "\fafc";
}
.bxs-layout-search:before {
    content: "\fafd";
}
.bxs-layout:before {
    content: "\fafe";
}
.bxs-leaf-alt:before {
    content: "\faff";
}
.bxs-leaf:before {
    content: "\fb00";
}
.bxs-left-indent:before {
    content: "\fb01";
}
.bxs-lemon:before {
    content: "\fb02";
}
.bxs-letter-spacing-alt:before {
    content: "\fb03";
}
.bxs-letter-spacing:before {
    content: "\fb04";
}
.bxs-light-bulb-alt:before {
    content: "\fb05";
}
.bxs-light-bulb-on:before {
    content: "\fb06";
}
.bxs-light-bulb:before {
    content: "\fb07";
}
.bxs-lightbulb-alt-2:before {
    content: "\fb08";
}
.bxs-like:before {
    content: "\fb09";
}
.bxs-line-chart-square:before {
    content: "\fb0a";
}
.bxs-line-spacing-alt:before {
    content: "\fb0b";
}
.bxs-line-spacing:before {
    content: "\fb0c";
}
.bxs-link-alt:before {
    content: "\fb0d";
}
.bxs-link-break:before {
    content: "\fb0e";
}
.bxs-link:before {
    content: "\fb0f";
}
.bxs-lira:before {
    content: "\fb10";
}
.bxs-list-minus:before {
    content: "\fb11";
}
.bxs-list-music:before {
    content: "\fb12";
}
.bxs-list-ol:before {
    content: "\fb13";
}
.bxs-list-play:before {
    content: "\fb14";
}
.bxs-list-plus:before {
    content: "\fb15";
}
.bxs-list-square:before {
    content: "\fb16";
}
.bxs-list-ul-square:before {
    content: "\fb17";
}
.bxs-list-ul:before {
    content: "\fb18";
}
.bxs-list-x:before {
    content: "\fb19";
}
.bxs-list:before {
    content: "\fb1a";
}
.bxs-loader-dots:before {
    content: "\fb1b";
}
.bxs-loader-lines:before {
    content: "\fb1c";
}
.bxs-location-alt-2:before {
    content: "\fb1d";
}
.bxs-location-alt:before {
    content: "\fb1f";
}
.bxs-location-blank:before {
    content: "\fb20";
}
.bxs-location-check:before {
    content: "\fb21";
}
.bxs-location-pin:before {
    content: "\fb22";
}
.bxs-location-plus:before {
    content: "\fb23";
}
.bxs-location-x:before {
    content: "\fb24";
}
.bxs-location:before {
    content: "\fb25";
}
.bxs-lock-keyhole-open-alt:before {
    content: "\fb26";
}
.bxs-lock-keyhole-open:before {
    content: "\fb27";
}
.bxs-lock-keyhole:before {
    content: "\fb28";
}
.bxs-lock-open-alt:before {
    content: "\fb29";
}
.bxs-lock-open:before {
    content: "\fb2a";
}
.bxs-lock:before {
    content: "\fb2b";
}
.bxs-lotion:before {
    content: "\fb2c";
}
.bxs-low-vision:before {
    content: "\fb2d";
}
.bxs-lowercase:before {
    content: "\fb2e";
}
.bxs-luggage:before {
    content: "\fb2f";
}
.bxs-lungs:before {
    content: "\fb30";
}
.bxs-magic-wand:before {
    content: "\fb31";
}
.bxs-magnet:before {
    content: "\fb32";
}
.bxs-mail-open:before {
    content: "\fb33";
}
.bxs-male:before {
    content: "\fb34";
}
.bxs-man-woman:before {
    content: "\fb35";
}
.bxs-man:before {
    content: "\fb36";
}
.bxs-map:before {
    content: "\fb37";
}
.bxs-margin-bottom:before {
    content: "\fb38";
}
.bxs-margin-left:before {
    content: "\fb39";
}
.bxs-margin-right:before {
    content: "\fb3a";
}
.bxs-margin-top:before {
    content: "\fb3b";
}
.bxs-martini:before {
    content: "\fb3c";
}
.bxs-mask:before {
    content: "\fb3d";
}
.bxs-math-alt:before {
    content: "\fb3e";
}
.bxs-math:before {
    content: "\fb3f";
}
.bxs-meat:before {
    content: "\fb40";
}
.bxs-medal-alt-2:before {
    content: "\fb41";
}
.bxs-medal-alt:before {
    content: "\fb42";
}
.bxs-medal-star-alt-2:before {
    content: "\fb43";
}
.bxs-medal-star-alt:before {
    content: "\fb44";
}
.bxs-medal-star:before {
    content: "\fb45";
}
.bxs-medal:before {
    content: "\fb46";
}
.bxs-medical-flask:before {
    content: "\fb47";
}
.bxs-medical-kit:before {
    content: "\fb48";
}
.bxs-megaphone-alt:before {
    content: "\fb49";
}
.bxs-megaphone:before {
    content: "\fb4a";
}
.bxs-meh-alt:before {
    content: "\fb4b";
}
.bxs-meh-blank:before {
    content: "\fb4c";
}
.bxs-meh:before {
    content: "\fb4d";
}
.bxs-menu-close:before {
    content: "\fb4e";
}
.bxs-menu-filter:before {
    content: "\fb4f";
}
.bxs-menu-left:before {
    content: "\fb50";
}
.bxs-menu-right:before {
    content: "\fb51";
}
.bxs-menu-search:before {
    content: "\fb52";
}
.bxs-menu-select:before {
    content: "\fb53";
}
.bxs-menu-wide:before {
    content: "\fb54";
}
.bxs-menu-wider:before {
    content: "\fb55";
}
.bxs-menu:before {
    content: "\fb56";
}
.bxs-mesh:before {
    content: "\fb57";
}
.bxs-message-bubble-captions:before {
    content: "\fb58";
}
.bxs-message-bubble-check:before {
    content: "\fb59";
}
.bxs-message-bubble-code:before {
    content: "\fb5a";
}
.bxs-message-bubble-detail:before {
    content: "\fb5b";
}
.bxs-message-bubble-dots-2:before {
    content: "\fb5c";
}
.bxs-message-bubble-dots:before {
    content: "\fb5d";
}
.bxs-message-bubble-edit:before {
    content: "\fb5e";
}
.bxs-message-bubble-exclamation:before {
    content: "\fb5f";
}
.bxs-message-bubble-heart:before {
    content: "\fb60";
}
.bxs-message-bubble-image:before {
    content: "\fb61";
}
.bxs-message-bubble-minus:before {
    content: "\fb62";
}
.bxs-message-bubble-notification:before {
    content: "\fb63";
}
.bxs-message-bubble-plus:before {
    content: "\fb64";
}
.bxs-message-bubble-question-mark:before {
    content: "\fb65";
}
.bxs-message-bubble-reply:before {
    content: "\fb66";
}
.bxs-message-bubble-star:before {
    content: "\fb67";
}
.bxs-message-bubble-x:before {
    content: "\fb68";
}
.bxs-message-bubble:before {
    content: "\fb69";
}
.bxs-message-captions:before {
    content: "\fb6a";
}
.bxs-message-check:before {
    content: "\fb6b";
}
.bxs-message-circle-captions:before {
    content: "\fb6c";
}
.bxs-message-circle-check:before {
    content: "\fb6d";
}
.bxs-message-circle-code:before {
    content: "\fb6e";
}
.bxs-message-circle-detail:before {
    content: "\fb6f";
}
.bxs-message-circle-dots-2:before {
    content: "\fb70";
}
.bxs-message-circle-dots:before {
    content: "\fb71";
}
.bxs-message-circle-edit:before {
    content: "\fb72";
}
.bxs-message-circle-exclamation:before {
    content: "\fb73";
}
.bxs-message-circle-heart:before {
    content: "\fb74";
}
.bxs-message-circle-image:before {
    content: "\fb75";
}
.bxs-message-circle-minus:before {
    content: "\fb76";
}
.bxs-message-circle-notification:before {
    content: "\fb77";
}
.bxs-message-circle-plus:before {
    content: "\fb78";
}
.bxs-message-circle-question-mark:before {
    content: "\fb79";
}
.bxs-message-circle-reply:before {
    content: "\fb7a";
}
.bxs-message-circle-star:before {
    content: "\fb7b";
}
.bxs-message-circle-x:before {
    content: "\fb7c";
}
.bxs-message-circle:before {
    content: "\fb7d";
}
.bxs-message-code:before {
    content: "\fb7e";
}
.bxs-message-detail:before {
    content: "\fb7f";
}
.bxs-message-dots-2:before {
    content: "\fb80";
}
.bxs-message-dots:before {
    content: "\fb81";
}
.bxs-message-edit:before {
    content: "\fb82";
}
.bxs-message-exclamation:before {
    content: "\fb83";
}
.bxs-message-heart:before {
    content: "\fb84";
}
.bxs-message-image:before {
    content: "\fb85";
}
.bxs-message-minus:before {
    content: "\fb86";
}
.bxs-message-notification:before {
    content: "\fb87";
}
.bxs-message-plus:before {
    content: "\fb88";
}
.bxs-message-question-mark:before {
    content: "\fb89";
}
.bxs-message-reply:before {
    content: "\fb8a";
}
.bxs-message-star:before {
    content: "\fb8b";
}
.bxs-message-x:before {
    content: "\fb8c";
}
.bxs-message:before {
    content: "\fb8d";
}
.bxs-meteor:before {
    content: "\fb8e";
}
.bxs-microchip:before {
    content: "\fb8f";
}
.bxs-microphone-alt-2:before {
    content: "\fb90";
}
.bxs-microphone-alt:before {
    content: "\fb91";
}
.bxs-microphone-big-alt:before {
    content: "\fb92";
}
.bxs-microphone-big:before {
    content: "\fb93";
}
.bxs-microphone-slash:before {
    content: "\fb94";
}
.bxs-microphone:before {
    content: "\fb95";
}
.bxs-microwave-oven:before {
    content: "\fb96";
}
.bxs-milk-bottle:before {
    content: "\fb97";
}
.bxs-minus-circle:before {
    content: "\fb98";
}
.bxs-minus-shield:before {
    content: "\fb99";
}
.bxs-minus-square:before {
    content: "\fb9a";
}
.bxs-minus:before {
    content: "\fb9b";
}
.bxs-mobile-alt-1:before {
    content: "\fb9c";
}
.bxs-mobile-alt:before {
    content: "\fb9d";
}
.bxs-mobile-back-alt-1:before {
    content: "\fb9e";
}
.bxs-mobile-back-alt:before {
    content: "\fb9f";
}
.bxs-mobile-back:before {
    content: "\fba0";
}
.bxs-mobile-ring:before {
    content: "\fba1";
}
.bxs-mobile:before {
    content: "\fba2";
}
.bxs-moon-crater:before {
    content: "\fba3";
}
.bxs-moon-phase-0:before {
    content: "\fba4";
}
.bxs-moon-phase-1:before {
    content: "\fba5";
}
.bxs-moon-phase-2:before {
    content: "\fba6";
}
.bxs-moon-phase-3:before {
    content: "\fba7";
}
.bxs-moon-phase-4:before {
    content: "\fba8";
}
.bxs-moon-phase-5:before {
    content: "\fba9";
}
.bxs-moon-phase-6:before {
    content: "\fbaa";
}
.bxs-moon-star:before {
    content: "\fbab";
}
.bxs-moon:before {
    content: "\fbac";
}
.bxs-mosque:before {
    content: "\fbad";
}
.bxs-motion-alt:before {
    content: "\fbae";
}
.bxs-motion:before {
    content: "\fbaf";
}
.bxs-motorcycle:before {
    content: "\fbb0";
}
.bxs-mountain-peak:before {
    content: "\fbb1";
}
.bxs-mountain-view:before {
    content: "\fbb2";
}
.bxs-mouse-alt:before {
    content: "\fbb3";
}
.bxs-mouse:before {
    content: "\fbb4";
}
.bxs-move-diagonal-left:before {
    content: "\fbb5";
}
.bxs-move-diagonal-right:before {
    content: "\fbb6";
}
.bxs-move-horizontal:before {
    content: "\fbb7";
}
.bxs-move-vertical:before {
    content: "\fbb8";
}
.bxs-move:before {
    content: "\fbb9";
}
.bxs-movie-play:before {
    content: "\fbba";
}
.bxs-movie:before {
    content: "\fbbb";
}
.bxs-music-alt:before {
    content: "\fbbc";
}
.bxs-music-library:before {
    content: "\fbbd";
}
.bxs-music:before {
    content: "\fbbe";
}
.bxs-network-chart:before {
    content: "\fbbf";
}
.bxs-news:before {
    content: "\fbc0";
}
.bxs-newspaper:before {
    content: "\fbc1";
}
.bxs-night-light:before {
    content: "\fbc2";
}
.bxs-no-entry:before {
    content: "\fbc3";
}
.bxs-noise:before {
    content: "\fbc4";
}
.bxs-note-book:before {
    content: "\fbc5";
}
.bxs-note:before {
    content: "\fbc6";
}
.bxs-notification-slash:before {
    content: "\fbc7";
}
.bxs-notification:before {
    content: "\fbc8";
}
.bxs-nut:before {
    content: "\fbc9";
}
.bxs-octopus:before {
    content: "\fbca";
}
.bxs-outdoor-dining:before {
    content: "\fbcb";
}
.bxs-outer-shadow:before {
    content: "\fbcc";
}
.bxs-oval-vertical:before {
    content: "\fbcd";
}
.bxs-oval:before {
    content: "\fbce";
}
.bxs-oven:before {
    content: "\fbcf";
}
.bxs-owl:before {
    content: "\fbd0";
}
.bxs-package:before {
    content: "\fbd1";
}
.bxs-pacman:before {
    content: "\fbd2";
}
.bxs-paint-alt:before {
    content: "\fbd3";
}
.bxs-paint-roll:before {
    content: "\fbd4";
}
.bxs-paint:before {
    content: "\fbd5";
}
.bxs-palette:before {
    content: "\fbd6";
}
.bxs-pant:before {
    content: "\fbd7";
}
.bxs-paper-plane:before {
    content: "\fbd8";
}
.bxs-paperclip:before {
    content: "\fbd9";
}
.bxs-paragraph-spacing:before {
    content: "\fbda";
}
.bxs-paragraph:before {
    content: "\fbdb";
}
.bxs-parent-child:before {
    content: "\fbdc";
}
.bxs-party:before {
    content: "\fbdd";
}
.bxs-paste:before {
    content: "\fbde";
}
.bxs-pause-circle:before {
    content: "\fbdf";
}
.bxs-pause:before {
    content: "\fbe0";
}
.bxs-paw-print:before {
    content: "\fbe1";
}
.bxs-pear:before {
    content: "\fbe2";
}
.bxs-pen-alt:before {
    content: "\fbe3";
}
.bxs-pen-draw:before {
    content: "\fbe4";
}
.bxs-pen-edit-circle:before {
    content: "\fbe5";
}
.bxs-pen-minus:before {
    content: "\fbe6";
}
.bxs-pen-plus:before {
    content: "\fbe7";
}
.bxs-pen:before {
    content: "\fbe8";
}
.bxs-pencil-circle:before {
    content: "\fbe9";
}
.bxs-pencil-draw:before {
    content: "\fbea";
}
.bxs-pencil-edit-circle:before {
    content: "\fbeb";
}
.bxs-pencil-sparkles:before {
    content: "\fbec";
}
.bxs-pencil-square:before {
    content: "\fbed";
}
.bxs-pencil:before {
    content: "\fbee";
}
.bxs-pentagon:before {
    content: "\fbef";
}
.bxs-people-diversity:before {
    content: "\fbf0";
}
.bxs-people-handshake:before {
    content: "\fbf1";
}
.bxs-people-heart:before {
    content: "\fbf2";
}
.bxs-percentage:before {
    content: "\fbf3";
}
.bxs-perspective:before {
    content: "\fbf4";
}
.bxs-petrol-pump:before {
    content: "\fbf5";
}
.bxs-pharmacy:before {
    content: "\fbf6";
}
.bxs-phone-book:before {
    content: "\fbf7";
}
.bxs-phone-forwarding:before {
    content: "\fbf8";
}
.bxs-phone-incoming:before {
    content: "\fbf9";
}
.bxs-phone-outgoing:before {
    content: "\fbfa";
}
.bxs-phone-plus:before {
    content: "\fbfb";
}
.bxs-phone-ring:before {
    content: "\fbfc";
}
.bxs-phone-x:before {
    content: "\fbfd";
}
.bxs-phone:before {
    content: "\fbfe";
}
.bxs-photo-album:before {
    content: "\fbff";
}
.bxs-piano-alt:before {
    content: "\fc00";
}
.bxs-piano-grand:before {
    content: "\fc01";
}
.bxs-piano:before {
    content: "\fc02";
}
.bxs-pickup-truck:before {
    content: "\fc03";
}
.bxs-picture-in-picture-close:before {
    content: "\fc04";
}
.bxs-picture-in-picture:before {
    content: "\fc05";
}
.bxs-pie-chart-alt-2:before {
    content: "\fc06";
}
.bxs-pie-chart-alt:before {
    content: "\fc07";
}
.bxs-pie-chart:before {
    content: "\fc08";
}
.bxs-piggy-bank:before {
    content: "\fc09";
}
.bxs-pill-bottle-alt:before {
    content: "\fc0a";
}
.bxs-pill-bottle:before {
    content: "\fc0b";
}
.bxs-pill:before {
    content: "\fc0c";
}
.bxs-pin-alt:before {
    content: "\fc0d";
}
.bxs-pin-slash-alt:before {
    content: "\fc0e";
}
.bxs-pin:before {
    content: "\fc0f";
}
.bxs-pizza-alt:before {
    content: "\fc10";
}
.bxs-pizza:before {
    content: "\fc11";
}
.bxs-plane-alt:before {
    content: "\fc12";
}
.bxs-plane-land:before {
    content: "\fc13";
}
.bxs-plane-take-off:before {
    content: "\fc14";
}
.bxs-plane:before {
    content: "\fc15";
}
.bxs-planet:before {
    content: "\fc16";
}
.bxs-plant-pot:before {
    content: "\fc17";
}
.bxs-play-circle-alt:before {
    content: "\fc18";
}
.bxs-play-circle:before {
    content: "\fc19";
}
.bxs-play:before {
    content: "\fc1a";
}
.bxs-plug-connect:before {
    content: "\fc1b";
}
.bxs-plus-big:before {
    content: "\fc1c";
}
.bxs-plus-circle:before {
    content: "\fc1d";
}
.bxs-plus-shield:before {
    content: "\fc1e";
}
.bxs-plus-square:before {
    content: "\fc1f";
}
.bxs-plus:before {
    content: "\fc20";
}
.bxs-podcast:before {
    content: "\fc21";
}
.bxs-polar-chart:before {
    content: "\fc22";
}
.bxs-poll:before {
    content: "\fc23";
}
.bxs-polygon:before {
    content: "\fc24";
}
.bxs-popsicle:before {
    content: "\fc25";
}
.bxs-pound:before {
    content: "\fc26";
}
.bxs-prawn:before {
    content: "\fc27";
}
.bxs-price-tag-alt:before {
    content: "\fc28";
}
.bxs-price-tag:before {
    content: "\fc29";
}
.bxs-print-dollar:before {
    content: "\fc2a";
}
.bxs-printer:before {
    content: "\fc2b";
}
.bxs-psychology:before {
    content: "\fc2c";
}
.bxs-puck:before {
    content: "\fc2d";
}
.bxs-pulse:before {
    content: "\fc2e";
}
.bxs-pyramid:before {
    content: "\fc2f";
}
.bxs-qr-scan:before {
    content: "\fc30";
}
.bxs-qr:before {
    content: "\fc31";
}
.bxs-queue:before {
    content: "\fc32";
}
.bxs-quote-left-alt:before {
    content: "\fc33";
}
.bxs-quote-left:before {
    content: "\fc34";
}
.bxs-quote-right-alt:before {
    content: "\fc35";
}
.bxs-quote-right:before {
    content: "\fc36";
}
.bxs-quote-single-left:before {
    content: "\fc37";
}
.bxs-quote-single-right:before {
    content: "\fc38";
}
.bxs-radar:before {
    content: "\fc39";
}
.bxs-radiation:before {
    content: "\fc3a";
}
.bxs-radio-circle-marked:before {
    content: "\fc3b";
}
.bxs-radio-circle:before {
    content: "\fc3c";
}
.bxs-radio:before {
    content: "\fc3d";
}
.bxs-rainbow:before {
    content: "\fc3e";
}
.bxs-reading-glass:before {
    content: "\fc3f";
}
.bxs-reading:before {
    content: "\fc40";
}
.bxs-receipt:before {
    content: "\fc41";
}
.bxs-rectangle-vertical:before {
    content: "\fc42";
}
.bxs-rectangle-wide:before {
    content: "\fc43";
}
.bxs-rectangle:before {
    content: "\fc44";
}
.bxs-recycle:before {
    content: "\fc45";
}
.bxs-redo-alt:before {
    content: "\fc46";
}
.bxs-redo-stroke-alt:before {
    content: "\fc47";
}
.bxs-redo-stroke:before {
    content: "\fc48";
}
.bxs-redo:before {
    content: "\fc49";
}
.bxs-refresh-ccw-alt-dot:before {
    content: "\fc4a";
}
.bxs-refresh-ccw-alt:before {
    content: "\fc4b";
}
.bxs-refresh-ccw-dot:before {
    content: "\fc4c";
}
.bxs-refresh-ccw:before {
    content: "\fc4d";
}
.bxs-refresh-cw-alt-dot:before {
    content: "\fc4e";
}
.bxs-refresh-cw-alt:before {
    content: "\fc4f";
}
.bxs-refresh-cw-dot:before {
    content: "\fc50";
}
.bxs-refresh-cw:before {
    content: "\fc51";
}
.bxs-registered:before {
    content: "\fc52";
}
.bxs-rename:before {
    content: "\fc53";
}
.bxs-repeat-alt-2:before {
    content: "\fc54";
}
.bxs-repeat-alt:before {
    content: "\fc55";
}
.bxs-repeat:before {
    content: "\fc56";
}
.bxs-reply-big:before {
    content: "\fc57";
}
.bxs-reply-stroke:before {
    content: "\fc58";
}
.bxs-reply:before {
    content: "\fc59";
}
.bxs-report:before {
    content: "\fc5a";
}
.bxs-rewind-circle:before {
    content: "\fc5b";
}
.bxs-rewind:before {
    content: "\fc5c";
}
.bxs-rfid:before {
    content: "\fc5d";
}
.bxs-rgb:before {
    content: "\fc5e";
}
.bxs-right-angle-triangle-half:before {
    content: "\fc5f";
}
.bxs-right-angle-triangle:before {
    content: "\fc60";
}
.bxs-right-indent:before {
    content: "\fc61";
}
.bxs-robot:before {
    content: "\fc62";
}
.bxs-rocket-alt:before {
    content: "\fc63";
}
.bxs-rocket:before {
    content: "\fc64";
}
.bxs-rotate-ccw-10:before {
    content: "\fc65";
}
.bxs-rotate-ccw-30:before {
    content: "\fc66";
}
.bxs-rotate-ccw-5:before {
    content: "\fc67";
}
.bxs-rotate-ccw-dot:before {
    content: "\fc68";
}
.bxs-rotate-ccw:before {
    content: "\fc69";
}
.bxs-rotate-cw-10:before {
    content: "\fc6a";
}
.bxs-rotate-cw-30:before {
    content: "\fc6b";
}
.bxs-rotate-cw-5:before {
    content: "\fc6c";
}
.bxs-rotate-cw-dot:before {
    content: "\fc6d";
}
.bxs-rotate-cw:before {
    content: "\fc6e";
}
.bxs-rotate-square-ccw:before {
    content: "\fc6f";
}
.bxs-rotate-square-cw:before {
    content: "\fc70";
}
.bxs-row-resize:before {
    content: "\fc71";
}
.bxs-rows-3:before {
    content: "\fc72";
}
.bxs-rows-4:before {
    content: "\fc73";
}
.bxs-rows:before {
    content: "\fc74";
}
.bxs-rss:before {
    content: "\fc75";
}
.bxs-ruble:before {
    content: "\fc76";
}
.bxs-rugby-ball:before {
    content: "\fc77";
}
.bxs-ruler:before {
    content: "\fc78";
}
.bxs-running:before {
    content: "\fc79";
}
.bxs-rupee:before {
    content: "\fc7a";
}
.bxs-sad:before {
    content: "\fc7b";
}
.bxs-safe:before {
    content: "\fc7c";
}
.bxs-sandwich:before {
    content: "\fc7d";
}
.bxs-sapling:before {
    content: "\fc7e";
}
.bxs-save:before {
    content: "\fc7f";
}
.bxs-scale:before {
    content: "\fc80";
}
.bxs-scan-ar:before {
    content: "\fc81";
}
.bxs-scan-barcode:before {
    content: "\fc82";
}
.bxs-scan-detail:before {
    content: "\fc83";
}
.bxs-scan-face:before {
    content: "\fc84";
}
.bxs-scan-search:before {
    content: "\fc85";
}
.bxs-scan:before {
    content: "\fc86";
}
.bxs-school-bus:before {
    content: "\fc87";
}
.bxs-school:before {
    content: "\fc88";
}
.bxs-science:before {
    content: "\fc89";
}
.bxs-scooter-delivery:before {
    content: "\fc8a";
}
.bxs-scooter:before {
    content: "\fc8b";
}
.bxs-screen-light:before {
    content: "\fc8c";
}
.bxs-screenshot:before {
    content: "\fc8d";
}
.bxs-scroll:before {
    content: "\fc8e";
}
.bxs-sd-card:before {
    content: "\fc8f";
}
.bxs-sea-view:before {
    content: "\fc90";
}
.bxs-seal:before {
    content: "\fc91";
}
.bxs-search-alt:before {
    content: "\fc92";
}
.bxs-search-big:before {
    content: "\fc93";
}
.bxs-search-code:before {
    content: "\fc94";
}
.bxs-search-minus:before {
    content: "\fc95";
}
.bxs-search-plus:before {
    content: "\fc96";
}
.bxs-search:before {
    content: "\fc97";
}
.bxs-select-all:before {
    content: "\fc98";
}
.bxs-select-many:before {
    content: "\fc99";
}
.bxs-select-none:before {
    content: "\fc9a";
}
.bxs-select:before {
    content: "\fc9b";
}
.bxs-send-alt-2:before {
    content: "\fc9c";
}
.bxs-send-alt:before {
    content: "\fc9d";
}
.bxs-send:before {
    content: "\fc9e";
}
.bxs-server:before {
    content: "\fc9f";
}
.bxs-shadows:before {
    content: "\fca0";
}
.bxs-shape-exclude-alt:before {
    content: "\fca1";
}
.bxs-shape-exclude:before {
    content: "\fca2";
}
.bxs-shape-intersect-alt:before {
    content: "\fca3";
}
.bxs-shape-intersect:before {
    content: "\fca4";
}
.bxs-shape-outline-alt:before {
    content: "\fca5";
}
.bxs-shape-outline:before {
    content: "\fca6";
}
.bxs-shape-rotate-ccw:before {
    content: "\fca7";
}
.bxs-shape-rotate-cw:before {
    content: "\fca8";
}
.bxs-shape-subtract-alt:before {
    content: "\fca9";
}
.bxs-shape-subtract:before {
    content: "\fcaa";
}
.bxs-shape-trim-alt:before {
    content: "\fcab";
}
.bxs-shape-trim:before {
    content: "\fcac";
}
.bxs-shape-unite-alt:before {
    content: "\fcad";
}
.bxs-shape-unite:before {
    content: "\fcae";
}
.bxs-shapes-alt:before {
    content: "\fcaf";
}
.bxs-shapes:before {
    content: "\fcb0";
}
.bxs-share:before {
    content: "\fcb1";
}
.bxs-shekel:before {
    content: "\fcb2";
}
.bxs-shield-alt-2:before {
    content: "\fcb3";
}
.bxs-shield-alt:before {
    content: "\fcb4";
}
.bxs-shield-circle:before {
    content: "\fcb5";
}
.bxs-shield-half:before {
    content: "\fcb6";
}
.bxs-shield-quarter:before {
    content: "\fcb7";
}
.bxs-shield:before {
    content: "\fcb8";
}
.bxs-ship:before {
    content: "\fcb9";
}
.bxs-shocked:before {
    content: "\fcba";
}
.bxs-shopping-bag-alt:before {
    content: "\fcbb";
}
.bxs-shopping-bag:before {
    content: "\fcbc";
}
.bxs-shower:before {
    content: "\fcbd";
}
.bxs-shrink-left:before {
    content: "\fcbe";
}
.bxs-shrink-right:before {
    content: "\fcbf";
}
.bxs-shuffle:before {
    content: "\fcc0";
}
.bxs-shutter-alt:before {
    content: "\fcc1";
}
.bxs-shutter:before {
    content: "\fcc2";
}
.bxs-shuttlecock:before {
    content: "\fcc3";
}
.bxs-sidebar-right:before {
    content: "\fcc4";
}
.bxs-sidebar:before {
    content: "\fcc5";
}
.bxs-signal-1:before {
    content: "\fcc6";
}
.bxs-signal-2:before {
    content: "\fcc7";
}
.bxs-signal-3:before {
    content: "\fcc8";
}
.bxs-signal-4:before {
    content: "\fcc9";
}
.bxs-signal-5:before {
    content: "\fcca";
}
.bxs-signal-slash:before {
    content: "\fccb";
}
.bxs-signature:before {
    content: "\fccc";
}
.bxs-siren-alt:before {
    content: "\fccd";
}
.bxs-siren:before {
    content: "\fcce";
}
.bxs-sitemap:before {
    content: "\fccf";
}
.bxs-size-distort:before {
    content: "\fcd0";
}
.bxs-size-freeform:before {
    content: "\fcd1";
}
.bxs-size-uniform:before {
    content: "\fcd2";
}
.bxs-skateboard:before {
    content: "\fcd3";
}
.bxs-skip-next-circle:before {
    content: "\fcd4";
}
.bxs-skip-next:before {
    content: "\fcd5";
}
.bxs-skip-previous-circle:before {
    content: "\fcd6";
}
.bxs-skip-previous:before {
    content: "\fcd7";
}
.bxs-skirt:before {
    content: "\fcd8";
}
.bxs-sleepy:before {
    content: "\fcd9";
}
.bxs-slice:before {
    content: "\fcda";
}
.bxs-slider-alt:before {
    content: "\fcdb";
}
.bxs-slider-vertical-alt:before {
    content: "\fcdc";
}
.bxs-slider-vertical:before {
    content: "\fcdd";
}
.bxs-slider:before {
    content: "\fcde";
}
.bxs-slideshow:before {
    content: "\fcdf";
}
.bxs-smile:before {
    content: "\fce0";
}
.bxs-smoke-alarm:before {
    content: "\fce1";
}
.bxs-sneaker:before {
    content: "\fce2";
}
.bxs-snowflake:before {
    content: "\fce3";
}
.bxs-sock:before {
    content: "\fce4";
}
.bxs-solar-panel:before {
    content: "\fce5";
}
.bxs-spa:before {
    content: "\fce6";
}
.bxs-spacebar:before {
    content: "\fce7";
}
.bxs-spade:before {
    content: "\fce8";
}
.bxs-spanner:before {
    content: "\fce9";
}
.bxs-sparkle-circle:before {
    content: "\fcea";
}
.bxs-sparkle-square:before {
    content: "\fceb";
}
.bxs-sparkle:before {
    content: "\fcec";
}
.bxs-sparkles-alt:before {
    content: "\fced";
}
.bxs-sparkles:before {
    content: "\fcee";
}
.bxs-speaker:before {
    content: "\fcef";
}
.bxs-split:before {
    content: "\fcf0";
}
.bxs-spoon:before {
    content: "\fcf1";
}
.bxs-spray-can:before {
    content: "\fcf2";
}
.bxs-square-dashed-half:before {
    content: "\fcf3";
}
.bxs-square-dashed:before {
    content: "\fcf4";
}
.bxs-square-rounded:before {
    content: "\fcf5";
}
.bxs-square-small:before {
    content: "\fcf6";
}
.bxs-square:before {
    content: "\fcf7";
}
.bxs-squircle:before {
    content: "\fcf8";
}
.bxs-stadium:before {
    content: "\fcf9";
}
.bxs-stamp:before {
    content: "\fcfa";
}
.bxs-star-circle:before {
    content: "\fcfb";
}
.bxs-star-half:before {
    content: "\fcfc";
}
.bxs-star-square:before {
    content: "\fcfd";
}
.bxs-star:before {
    content: "\fcfe";
}
.bxs-station:before {
    content: "\fcff";
}
.bxs-steering-wheel:before {
    content: "\fd00";
}
.bxs-steps-down:before {
    content: "\fd01";
}
.bxs-steps-up:before {
    content: "\fd02";
}
.bxs-sticker:before {
    content: "\fd03";
}
.bxs-stop-circle:before {
    content: "\fd04";
}
.bxs-stop:before {
    content: "\fd05";
}
.bxs-stopwatch:before {
    content: "\fd06";
}
.bxs-store-alt-2:before {
    content: "\fd07";
}
.bxs-store-alt:before {
    content: "\fd08";
}
.bxs-store:before {
    content: "\fd09";
}
.bxs-strategy:before {
    content: "\fd0a";
}
.bxs-street-view:before {
    content: "\fd0b";
}
.bxs-strikethrough:before {
    content: "\fd0c";
}
.bxs-stroke-drawing:before {
    content: "\fd0d";
}
.bxs-stroke-freehand:before {
    content: "\fd0e";
}
.bxs-stroke-ink:before {
    content: "\fd0f";
}
.bxs-stroke-pen:before {
    content: "\fd10";
}
.bxs-subscript:before {
    content: "\fd11";
}
.bxs-subway:before {
    content: "\fd12";
}
.bxs-sun-bright:before {
    content: "\fd13";
}
.bxs-sun-dim:before {
    content: "\fd14";
}
.bxs-sun-drizzle:before {
    content: "\fd15";
}
.bxs-sun-fog:before {
    content: "\fd16";
}
.bxs-sun-rain-wind:before {
    content: "\fd17";
}
.bxs-sun-rain:before {
    content: "\fd18";
}
.bxs-sun-rise:before {
    content: "\fd19";
}
.bxs-sun-set:before {
    content: "\fd1a";
}
.bxs-sun-snow:before {
    content: "\fd1b";
}
.bxs-sun:before {
    content: "\fd1c";
}
.bxs-superscript:before {
    content: "\fd1d";
}
.bxs-surfboard:before {
    content: "\fd1e";
}
.bxs-sushi:before {
    content: "\fd1f";
}
.bxs-swap-diagonal:before {
    content: "\fd20";
}
.bxs-swap-horizontal:before {
    content: "\fd21";
}
.bxs-swap-vertical:before {
    content: "\fd22";
}
.bxs-swatch:before {
    content: "\fd23";
}
.bxs-swimming-pool:before {
    content: "\fd24";
}
.bxs-swimming:before {
    content: "\fd25";
}
.bxs-sword-alt:before {
    content: "\fd26";
}
.bxs-sword:before {
    content: "\fd27";
}
.bxs-syringe:before {
    content: "\fd28";
}
.bxs-t-shirt:before {
    content: "\fd29";
}
.bxs-tab:before {
    content: "\fd2a";
}
.bxs-table-cells-large:before {
    content: "\fd2b";
}
.bxs-table-cells:before {
    content: "\fd2c";
}
.bxs-table-columns-merge:before {
    content: "\fd2d";
}
.bxs-table-columns-split:before {
    content: "\fd2e";
}
.bxs-table-columns:before {
    content: "\fd2f";
}
.bxs-table-layout:before {
    content: "\fd30";
}
.bxs-table-list:before {
    content: "\fd31";
}
.bxs-table-rows-merge:before {
    content: "\fd32";
}
.bxs-table-rows-split:before {
    content: "\fd33";
}
.bxs-table-rows:before {
    content: "\fd34";
}
.bxs-table-tennis:before {
    content: "\fd35";
}
.bxs-table:before {
    content: "\fd36";
}
.bxs-tablet:before {
    content: "\fd37";
}
.bxs-tabs:before {
    content: "\fd38";
}
.bxs-tachometer-alt:before {
    content: "\fd39";
}
.bxs-tachometer:before {
    content: "\fd3a";
}
.bxs-taco:before {
    content: "\fd3b";
}
.bxs-tag-alt:before {
    content: "\fd3c";
}
.bxs-tag-x:before {
    content: "\fd3d";
}
.bxs-tag:before {
    content: "\fd3e";
}
.bxs-takeaway:before {
    content: "\fd3f";
}
.bxs-target:before {
    content: "\fd40";
}
.bxs-taxi:before {
    content: "\fd41";
}
.bxs-temple:before {
    content: "\fd42";
}
.bxs-tennis-ball-alt:before {
    content: "\fd43";
}
.bxs-tennis-ball:before {
    content: "\fd44";
}
.bxs-tennis:before {
    content: "\fd45";
}
.bxs-tent:before {
    content: "\fd46";
}
.bxs-terminal:before {
    content: "\fd47";
}
.bxs-test-tube:before {
    content: "\fd48";
}
.bxs-text-height:before {
    content: "\fd49";
}
.bxs-text-underline:before {
    content: "\fd4a";
}
.bxs-text-width:before {
    content: "\fd4b";
}
.bxs-texture:before {
    content: "\fd4c";
}
.bxs-thermometer:before {
    content: "\fd4d";
}
.bxs-thread-roll:before {
    content: "\fd4e";
}
.bxs-thunder:before {
    content: "\fd4f";
}
.bxs-ticket-star:before {
    content: "\fd50";
}
.bxs-ticket:before {
    content: "\fd51";
}
.bxs-tickets:before {
    content: "\fd52";
}
.bxs-timer:before {
    content: "\fd53";
}
.bxs-tiny-home:before {
    content: "\fd54";
}
.bxs-tired:before {
    content: "\fd55";
}
.bxs-toggle-left:before {
    content: "\fd56";
}
.bxs-toggle-right:before {
    content: "\fd57";
}
.bxs-toggles:before {
    content: "\fd58";
}
.bxs-toilet-roll:before {
    content: "\fd59";
}
.bxs-tooth:before {
    content: "\fd5a";
}
.bxs-torch:before {
    content: "\fd5b";
}
.bxs-tornado:before {
    content: "\fd5c";
}
.bxs-torus:before {
    content: "\fd5d";
}
.bxs-towel:before {
    content: "\fd5e";
}
.bxs-toy-car:before {
    content: "\fd5f";
}
.bxs-traffic-barrier:before {
    content: "\fd60";
}
.bxs-traffic-cone:before {
    content: "\fd61";
}
.bxs-train:before {
    content: "\fd62";
}
.bxs-tram:before {
    content: "\fd63";
}
.bxs-transgender:before {
    content: "\fd64";
}
.bxs-translate:before {
    content: "\fd65";
}
.bxs-transparency:before {
    content: "\fd66";
}
.bxs-trash-alt:before {
    content: "\fd67";
}
.bxs-trash-x:before {
    content: "\fd68";
}
.bxs-trash:before {
    content: "\fd69";
}
.bxs-treasure-chest:before {
    content: "\fd6a";
}
.bxs-tree-alt:before {
    content: "\fd6b";
}
.bxs-tree:before {
    content: "\fd6c";
}
.bxs-trees:before {
    content: "\fd6d";
}
.bxs-trending-down:before {
    content: "\fd6e";
}
.bxs-trending-up:before {
    content: "\fd6f";
}
.bxs-triangle-half:before {
    content: "\fd70";
}
.bxs-triangle:before {
    content: "\fd71";
}
.bxs-trip:before {
    content: "\fd72";
}
.bxs-trophy-star:before {
    content: "\fd73";
}
.bxs-trophy:before {
    content: "\fd74";
}
.bxs-truck:before {
    content: "\fd75";
}
.bxs-turkey-meat:before {
    content: "\fd76";
}
.bxs-turn-down:before {
    content: "\fd77";
}
.bxs-turn-left:before {
    content: "\fd78";
}
.bxs-turn-right:before {
    content: "\fd79";
}
.bxs-turn-up:before {
    content: "\fd7a";
}
.bxs-tv-alt:before {
    content: "\fd7b";
}
.bxs-tv:before {
    content: "\fd7c";
}
.bxs-ufo:before {
    content: "\fd7d";
}
.bxs-umbrella-alt:before {
    content: "\fd7e";
}
.bxs-umbrella:before {
    content: "\fd7f";
}
.bxs-underline-dashed:before {
    content: "\fd80";
}
.bxs-underline-dotted:before {
    content: "\fd81";
}
.bxs-underline-wavy:before {
    content: "\fd82";
}
.bxs-underline:before {
    content: "\fd83";
}
.bxs-undershirt:before {
    content: "\fd84";
}
.bxs-undo-alt:before {
    content: "\fd85";
}
.bxs-undo-stroke-alt:before {
    content: "\fd86";
}
.bxs-undo-stroke:before {
    content: "\fd87";
}
.bxs-undo:before {
    content: "\fd88";
}
.bxs-universal-access:before {
    content: "\fd89";
}
.bxs-unlink-alt:before {
    content: "\fd8a";
}
.bxs-unlink:before {
    content: "\fd8b";
}
.bxs-uppercase:before {
    content: "\fd8c";
}
.bxs-upside-down:before {
    content: "\fd8d";
}
.bxs-usb:before {
    content: "\fd8e";
}
.bxs-user-check:before {
    content: "\fd8f";
}
.bxs-user-circle:before {
    content: "\fd90";
}
.bxs-user-hexagon:before {
    content: "\fd91";
}
.bxs-user-id-card:before {
    content: "\fd92";
}
.bxs-user-minus:before {
    content: "\fd93";
}
.bxs-user-plus:before {
    content: "\fd94";
}
.bxs-user-search:before {
    content: "\fd95";
}
.bxs-user-square:before {
    content: "\fd96";
}
.bxs-user-voice:before {
    content: "\fd97";
}
.bxs-user-x:before {
    content: "\fd98";
}
.bxs-user:before {
    content: "\fd99";
}
.bxs-van:before {
    content: "\fd9a";
}
.bxs-vector-square:before {
    content: "\fd9b";
}
.bxs-vector-triangle:before {
    content: "\fd9c";
}
.bxs-vector:before {
    content: "\fd9d";
}
.bxs-vertical-align-bottom:before {
    content: "\fd9e";
}
.bxs-vertical-align-center:before {
    content: "\fd9f";
}
.bxs-vertical-align-top:before {
    content: "\fda0";
}
.bxs-vertical-bottom:before {
    content: "\fda1";
}
.bxs-vertical-center:before {
    content: "\fda2";
}
.bxs-vertical-distribute-bottom:before {
    content: "\fda3";
}
.bxs-vertical-distribute-center:before {
    content: "\fda4";
}
.bxs-vertical-distribute-top:before {
    content: "\fda5";
}
.bxs-vertical-spacing:before {
    content: "\fda6";
}
.bxs-vertical-top:before {
    content: "\fda7";
}
.bxs-vial-alt:before {
    content: "\fda8";
}
.bxs-vial:before {
    content: "\fda9";
}
.bxs-video-cinema:before {
    content: "\fdaa";
}
.bxs-video-plus:before {
    content: "\fdab";
}
.bxs-video-slash:before {
    content: "\fdac";
}
.bxs-video:before {
    content: "\fdad";
}
.bxs-vignette:before {
    content: "\fdae";
}
.bxs-virus-slash:before {
    content: "\fdaf";
}
.bxs-virus:before {
    content: "\fdb0";
}
.bxs-voicemail:before {
    content: "\fdb1";
}
.bxs-volleyball:before {
    content: "\fdb2";
}
.bxs-volume-full:before {
    content: "\fdb3";
}
.bxs-volume-low:before {
    content: "\fdb4";
}
.bxs-volume-mute:before {
    content: "\fdb5";
}
.bxs-volume:before {
    content: "\fdb6";
}
.bxs-vr-goggles:before {
    content: "\fdb7";
}
.bxs-vr-headset:before {
    content: "\fdb8";
}
.bxs-walking:before {
    content: "\fdb9";
}
.bxs-wall:before {
    content: "\fdba";
}
.bxs-wallet-alt:before {
    content: "\fdbb";
}
.bxs-wallet-cards:before {
    content: "\fdbc";
}
.bxs-wallet-note:before {
    content: "\fdbd";
}
.bxs-wallet:before {
    content: "\fdbe";
}
.bxs-warehouse:before {
    content: "\fdbf";
}
.bxs-washer:before {
    content: "\fdc0";
}
.bxs-water-drop-alt:before {
    content: "\fdc1";
}
.bxs-water-drop-half:before {
    content: "\fdc2";
}
.bxs-water-drop:before {
    content: "\fdc3";
}
.bxs-water-spray:before {
    content: "\fdc4";
}
.bxs-water:before {
    content: "\fdc5";
}
.bxs-watermelon:before {
    content: "\fdc6";
}
.bxs-waveform:before {
    content: "\fdc7";
}
.bxs-webcam:before {
    content: "\fdc8";
}
.bxs-whiteboard-alt:before {
    content: "\fdc9";
}
.bxs-whiteboard:before {
    content: "\fdca";
}
.bxs-widget-horizontal:before {
    content: "\fdcb";
}
.bxs-widget-small:before {
    content: "\fdcc";
}
.bxs-widget-vertical:before {
    content: "\fdcd";
}
.bxs-widget:before {
    content: "\fdce";
}
.bxs-wifi-0:before {
    content: "\fdcf";
}
.bxs-wifi-1:before {
    content: "\fdd0";
}
.bxs-wifi-2:before {
    content: "\fdd1";
}
.bxs-wifi-slash:before {
    content: "\fdd2";
}
.bxs-wifi:before {
    content: "\fdd3";
}
.bxs-wind:before {
    content: "\fdd4";
}
.bxs-window-arrow-in:before {
    content: "\fdd5";
}
.bxs-window-arrow-out:before {
    content: "\fdd6";
}
.bxs-window-mac-alt:before {
    content: "\fdd7";
}
.bxs-window-mac:before {
    content: "\fdd8";
}
.bxs-window:before {
    content: "\fdd9";
}
.bxs-windows:before {
    content: "\fdda";
}
.bxs-wine-alt:before {
    content: "\fddb";
}
.bxs-wine:before {
    content: "\fddc";
}
.bxs-wink-smile:before {
    content: "\fddd";
}
.bxs-wink-tongue:before {
    content: "\fdde";
}
.bxs-woman:before {
    content: "\fddf";
}
.bxs-won:before {
    content: "\fde0";
}
.bxs-wrist-watch-alt:before {
    content: "\fde1";
}
.bxs-wrist-watch-round-alt:before {
    content: "\fde2";
}
.bxs-wrist-watch-round:before {
    content: "\fde3";
}
.bxs-wrist-watch:before {
    content: "\fde4";
}
.bxs-x-circle:before {
    content: "\fde5";
}
.bxs-x-shield:before {
    content: "\fde6";
}
.bxs-x-square:before {
    content: "\fde7";
}
.bxs-x:before {
    content: "\fde8";
}
.bxs-yarn-ball:before {
    content: "\fde9";
}
.bxs-yen:before {
    content: "\fdea";
}
.bxs-yin-yang:before {
    content: "\fdeb";
}
