@font-face {
 font-family: "boxicons-brands";
 src: url("./boxicons-brands.ttf?********************************") format("truetype"),
url("./boxicons-brands.woff?********************************") format("woff"),
url("./boxicons-brands.woff2?********************************") format("woff2");
}


.bxl {
 font-family: boxicons-brands !important;
 font-style: normal;
 font-weight: normal;
 font-variant: normal;
 text-transform: none;
 line-height: 1;
 display:inline-block;
 speak:none;
 -webkit-font-smoothing: antialiased;
 -moz-osx-font-smoothing: grayscale;
}

.bxl.variable-selector-00:before {
    content: "\fb1e";
}
.bxl.bx-500px:before {
    content: "\f101";
}
.bxl.bx-99designs:before {
    content: "\f102";
}
.bxl.bx-adobe:before {
    content: "\f103";
}
.bxl.bx-airbnb:before {
    content: "\f104";
}
.bxl.bx-algolia:before {
    content: "\f105";
}
.bxl.bx-amazon:before {
    content: "\f106";
}
.bxl.bx-android:before {
    content: "\f107";
}
.bxl.bx-angular:before {
    content: "\f108";
}
.bxl.bx-anthropic:before {
    content: "\f109";
}
.bxl.bx-apple-music:before {
    content: "\f10a";
}
.bxl.bx-apple:before {
    content: "\f10b";
}
.bxl.bx-arc-browser:before {
    content: "\f10c";
}
.bxl.bx-artstation:before {
    content: "\f10d";
}
.bxl.bx-asana:before {
    content: "\f10e";
}
.bxl.bx-atlassian:before {
    content: "\f10f";
}
.bxl.bx-atom-editor:before {
    content: "\f110";
}
.bxl.bx-audible:before {
    content: "\f111";
}
.bxl.bx-auth0:before {
    content: "\f112";
}
.bxl.bx-autodesk:before {
    content: "\f113";
}
.bxl.bx-aws:before {
    content: "\f114";
}
.bxl.bx-baidu:before {
    content: "\f115";
}
.bxl.bx-bash:before {
    content: "\f116";
}
.bxl.bx-behance:before {
    content: "\f117";
}
.bxl.bx-better-auth:before {
    content: "\f118";
}
.bxl.bx-bing:before {
    content: "\f119";
}
.bxl.bx-bitcoin-logo:before {
    content: "\f11a";
}
.bxl.bx-blender:before {
    content: "\f11b";
}
.bxl.bx-blogger:before {
    content: "\f11c";
}
.bxl.bx-bluesky:before {
    content: "\f11d";
}
.bxl.bx-bolt-b:before {
    content: "\f11e";
}
.bxl.bx-bootstrap:before {
    content: "\f11f";
}
.bxl.bx-boxicons:before {
    content: "\f120";
}
.bxl.bx-brave-browser:before {
    content: "\f121";
}
.bxl.bx-bun:before {
    content: "\f122";
}
.bxl.bx-buy-me-a-coffee:before {
    content: "\f123";
}
.bxl.bx-c-plus-plus:before {
    content: "\f124";
}
.bxl.bx-c-sharp:before {
    content: "\f125";
}
.bxl.bx-c:before {
    content: "\f126";
}
.bxl.bx-canva:before {
    content: "\f127";
}
.bxl.bx-chess-com:before {
    content: "\f128";
}
.bxl.bx-chrome:before {
    content: "\f129";
}
.bxl.bx-claude-ai:before {
    content: "\f12a";
}
.bxl.bx-clerk:before {
    content: "\f12b";
}
.bxl.bx-cloudflare:before {
    content: "\f12c";
}
.bxl.bx-codepen:before {
    content: "\f12d";
}
.bxl.bx-convex:before {
    content: "\f12e";
}
.bxl.bx-creative-commons:before {
    content: "\f12f";
}
.bxl.bx-crunchyroll:before {
    content: "\f130";
}
.bxl.bx-css3:before {
    content: "\f131";
}
.bxl.bx-dailymotion:before {
    content: "\f132";
}
.bxl.bx-deepmind:before {
    content: "\f133";
}
.bxl.bx-deepseek:before {
    content: "\f134";
}
.bxl.bx-deezer:before {
    content: "\f135";
}
.bxl.bx-deno:before {
    content: "\f136";
}
.bxl.bx-dev-to:before {
    content: "\f137";
}
.bxl.bx-deviantart:before {
    content: "\f138";
}
.bxl.bx-devpost:before {
    content: "\f139";
}
.bxl.bx-digg:before {
    content: "\f13a";
}
.bxl.bx-digitalocean:before {
    content: "\f13b";
}
.bxl.bx-discord-alt:before {
    content: "\f13c";
}
.bxl.bx-discord:before {
    content: "\f13d";
}
.bxl.bx-discourse:before {
    content: "\f13e";
}
.bxl.bx-django:before {
    content: "\f13f";
}
.bxl.bx-docker:before {
    content: "\f140";
}
.bxl.bx-dot-env:before {
    content: "\f141";
}
.bxl.bx-dribbble:before {
    content: "\f142";
}
.bxl.bx-drizzle-orm:before {
    content: "\f143";
}
.bxl.bx-dropbox:before {
    content: "\f144";
}
.bxl.bx-ebay:before {
    content: "\f145";
}
.bxl.bx-edge:before {
    content: "\f146";
}
.bxl.bx-etsy:before {
    content: "\f147";
}
.bxl.bx-expo:before {
    content: "\f148";
}
.bxl.bx-express-js:before {
    content: "\f149";
}
.bxl.bx-facebook-circle:before {
    content: "\f14a";
}
.bxl.bx-facebook-square:before {
    content: "\f14b";
}
.bxl.bx-facebook:before {
    content: "\f14c";
}
.bxl.bx-fastapi:before {
    content: "\f14d";
}
.bxl.bx-fastify:before {
    content: "\f14e";
}
.bxl.bx-figma-alt:before {
    content: "\f14f";
}
.bxl.bx-figma:before {
    content: "\f150";
}
.bxl.bx-firebase:before {
    content: "\f151";
}
.bxl.bx-firefox:before {
    content: "\f152";
}
.bxl.bx-fiverr:before {
    content: "\f153";
}
.bxl.bx-flask-old:before {
    content: "\f154";
}
.bxl.bx-flask:before {
    content: "\f155";
}
.bxl.bx-flickr-square:before {
    content: "\f156";
}
.bxl.bx-flickr:before {
    content: "\f157";
}
.bxl.bx-flutter:before {
    content: "\f158";
}
.bxl.bx-foursquare:before {
    content: "\f159";
}
.bxl.bx-framer:before {
    content: "\f15a";
}
.bxl.bx-gatsby-js:before {
    content: "\f15b";
}
.bxl.bx-gemini:before {
    content: "\f15c";
}
.bxl.bx-git:before {
    content: "\f15d";
}
.bxl.bx-github-copilot:before {
    content: "\f15e";
}
.bxl.bx-github:before {
    content: "\f15f";
}
.bxl.bx-gitlab:before {
    content: "\f160";
}
.bxl.bx-gmail:before {
    content: "\f161";
}
.bxl.bx-go-lang:before {
    content: "\f162";
}
.bxl.bx-google-cloud:before {
    content: "\f163";
}
.bxl.bx-google-pay:before {
    content: "\f164";
}
.bxl.bx-google:before {
    content: "\f165";
}
.bxl.bx-graphql:before {
    content: "\f166";
}
.bxl.bx-grok:before {
    content: "\f167";
}
.bxl.bx-groq-ai:before {
    content: "\f168";
}
.bxl.bx-gsap:before {
    content: "\f169";
}
.bxl.bx-gumroad:before {
    content: "\f16a";
}
.bxl.bx-hashnode:before {
    content: "\f16b";
}
.bxl.bx-hcaptcha:before {
    content: "\f16c";
}
.bxl.bx-heroku:before {
    content: "\f16d";
}
.bxl.bx-hono-js:before {
    content: "\f16e";
}
.bxl.bx-html5:before {
    content: "\f16f";
}
.bxl.bx-hugo:before {
    content: "\f170";
}
.bxl.bx-ibm:before {
    content: "\f171";
}
.bxl.bx-imdb:before {
    content: "\f172";
}
.bxl.bx-instagram-alt:before {
    content: "\f173";
}
.bxl.bx-instagram:before {
    content: "\f174";
}
.bxl.bx-internet-explorer:before {
    content: "\f175";
}
.bxl.bx-invision:before {
    content: "\f176";
}
.bxl.bx-java:before {
    content: "\f177";
}
.bxl.bx-javascript:before {
    content: "\f178";
}
.bxl.bx-joomla:before {
    content: "\f179";
}
.bxl.bx-jquery:before {
    content: "\f17a";
}
.bxl.bx-jsfiddle:before {
    content: "\f17b";
}
.bxl.bx-jwt:before {
    content: "\f17c";
}
.bxl.bx-kick:before {
    content: "\f17d";
}
.bxl.bx-kickstarter:before {
    content: "\f17e";
}
.bxl.bx-kotlin:before {
    content: "\f17f";
}
.bxl.bx-kubernetes:before {
    content: "\f180";
}
.bxl.bx-laravel:before {
    content: "\f181";
}
.bxl.bx-leetcode:before {
    content: "\f182";
}
.bxl.bx-lemon-squeezy:before {
    content: "\f183";
}
.bxl.bx-less:before {
    content: "\f184";
}
.bxl.bx-letterboxd:before {
    content: "\f185";
}
.bxl.bx-lichess:before {
    content: "\f186";
}
.bxl.bx-linear-app:before {
    content: "\f187";
}
.bxl.bx-linkedin-square:before {
    content: "\f188";
}
.bxl.bx-linkedin:before {
    content: "\f189";
}
.bxl.bx-linktree:before {
    content: "\f18a";
}
.bxl.bx-loom:before {
    content: "\f18b";
}
.bxl.bx-lottie-lab:before {
    content: "\f18c";
}
.bxl.bx-lyft:before {
    content: "\f18d";
}
.bxl.bx-magento:before {
    content: "\f18e";
}
.bxl.bx-mailchimp:before {
    content: "\f18f";
}
.bxl.bx-markdown:before {
    content: "\f190";
}
.bxl.bx-mastercard:before {
    content: "\f191";
}
.bxl.bx-mastodon:before {
    content: "\f192";
}
.bxl.bx-medium-old:before {
    content: "\f193";
}
.bxl.bx-medium-square:before {
    content: "\f194";
}
.bxl.bx-medium:before {
    content: "\f195";
}
.bxl.bx-messenger:before {
    content: "\f196";
}
.bxl.bx-meta:before {
    content: "\f197";
}
.bxl.bx-microsoft-teams:before {
    content: "\f198";
}
.bxl.bx-microsoft-windows:before {
    content: "\f199";
}
.bxl.bx-microsoft:before {
    content: "\f19a";
}
.bxl.bx-midjourney:before {
    content: "\f19b";
}
.bxl.bx-mongodb:before {
    content: "\f19c";
}
.bxl.bx-motion-js:before {
    content: "\f19d";
}
.bxl.bx-mozilla:before {
    content: "\f19e";
}
.bxl.bx-my-sql:before {
    content: "\f19f";
}
.bxl.bx-neon-tech:before {
    content: "\f1a0";
}
.bxl.bx-neovim:before {
    content: "\f1a1";
}
.bxl.bx-nest-js:before {
    content: "\f1a2";
}
.bxl.bx-netlify:before {
    content: "\f1a3";
}
.bxl.bx-next-js:before {
    content: "\f1a4";
}
.bxl.bx-nodejs:before {
    content: "\f1a5";
}
.bxl.bx-notion:before {
    content: "\f1a6";
}
.bxl.bx-npm:before {
    content: "\f1a7";
}
.bxl.bx-nuxt-js:before {
    content: "\f1a8";
}
.bxl.bx-ok-ru:before {
    content: "\f1a9";
}
.bxl.bx-ollama:before {
    content: "\f1aa";
}
.bxl.bx-openai:before {
    content: "\f1ab";
}
.bxl.bx-opensea:before {
    content: "\f1ac";
}
.bxl.bx-opera:before {
    content: "\f1ad";
}
.bxl.bx-paddle-p:before {
    content: "\f1ae";
}
.bxl.bx-patreon:before {
    content: "\f1af";
}
.bxl.bx-payload-cms:before {
    content: "\f1b0";
}
.bxl.bx-paypal:before {
    content: "\f1b1";
}
.bxl.bx-periscope:before {
    content: "\f1b2";
}
.bxl.bx-perplexity-ai:before {
    content: "\f1b3";
}
.bxl.bx-php:before {
    content: "\f1b4";
}
.bxl.bx-pinterest-alt:before {
    content: "\f1b5";
}
.bxl.bx-pinterest:before {
    content: "\f1b6";
}
.bxl.bx-planetscale:before {
    content: "\f1b7";
}
.bxl.bx-play-store:before {
    content: "\f1b8";
}
.bxl.bx-playstation:before {
    content: "\f1b9";
}
.bxl.bx-pocket:before {
    content: "\f1ba";
}
.bxl.bx-polar:before {
    content: "\f1bb";
}
.bxl.bx-postgresql:before {
    content: "\f1bc";
}
.bxl.bx-prisma-orm:before {
    content: "\f1bd";
}
.bxl.bx-product-hunt:before {
    content: "\f1be";
}
.bxl.bx-python:before {
    content: "\f1bf";
}
.bxl.bx-quora:before {
    content: "\f1c0";
}
.bxl.bx-radix-ui:before {
    content: "\f1c1";
}
.bxl.bx-railway:before {
    content: "\f1c2";
}
.bxl.bx-rasberry-pi:before {
    content: "\f1c3";
}
.bxl.bx-react-query:before {
    content: "\f1c4";
}
.bxl.bx-react-router:before {
    content: "\f1c5";
}
.bxl.bx-react:before {
    content: "\f1c6";
}
.bxl.bx-redbubble:before {
    content: "\f1c7";
}
.bxl.bx-reddit:before {
    content: "\f1c8";
}
.bxl.bx-redux:before {
    content: "\f1c9";
}
.bxl.bx-remix-js:before {
    content: "\f1ca";
}
.bxl.bx-replit:before {
    content: "\f1cb";
}
.bxl.bx-resend:before {
    content: "\f1cc";
}
.bxl.bx-roblox:before {
    content: "\f1cd";
}
.bxl.bx-sass:before {
    content: "\f1ce";
}
.bxl.bx-sentry:before {
    content: "\f1cf";
}
.bxl.bx-shadcn-ui:before {
    content: "\f1d0";
}
.bxl.bx-shopify:before {
    content: "\f1d1";
}
.bxl.bx-sketch:before {
    content: "\f1d2";
}
.bxl.bx-skype:before {
    content: "\f1d3";
}
.bxl.bx-slack-old:before {
    content: "\f1d4";
}
.bxl.bx-slack:before {
    content: "\f1d5";
}
.bxl.bx-snapchat:before {
    content: "\f1d6";
}
.bxl.bx-socket-io:before {
    content: "\f1d7";
}
.bxl.bx-soundcloud:before {
    content: "\f1d8";
}
.bxl.bx-spotify:before {
    content: "\f1d9";
}
.bxl.bx-spring-boot:before {
    content: "\f1da";
}
.bxl.bx-squarespace:before {
    content: "\f1db";
}
.bxl.bx-sst:before {
    content: "\f1dc";
}
.bxl.bx-stack-overflow:before {
    content: "\f1dd";
}
.bxl.bx-stackblitz:before {
    content: "\f1de";
}
.bxl.bx-steam:before {
    content: "\f1df";
}
.bxl.bx-stripe:before {
    content: "\f1e0";
}
.bxl.bx-supabase:before {
    content: "\f1e1";
}
.bxl.bx-svelte:before {
    content: "\f1e2";
}
.bxl.bx-tailwind-css:before {
    content: "\f1e3";
}
.bxl.bx-telegram:before {
    content: "\f1e4";
}
.bxl.bx-terraform:before {
    content: "\f1e5";
}
.bxl.bx-threads:before {
    content: "\f1e6";
}
.bxl.bx-three-js:before {
    content: "\f1e7";
}
.bxl.bx-tiktok:before {
    content: "\f1e8";
}
.bxl.bx-trello:before {
    content: "\f1e9";
}
.bxl.bx-trip-advisor:before {
    content: "\f1ea";
}
.bxl.bx-trpc:before {
    content: "\f1eb";
}
.bxl.bx-trustpilot:before {
    content: "\f1ec";
}
.bxl.bx-tumblr:before {
    content: "\f1ed";
}
.bxl.bx-tux:before {
    content: "\f1ee";
}
.bxl.bx-twitch:before {
    content: "\f1ef";
}
.bxl.bx-twitter-x:before {
    content: "\f1f0";
}
.bxl.bx-twitter:before {
    content: "\f1f1";
}
.bxl.bx-typescript:before {
    content: "\f1f2";
}
.bxl.bx-uber:before {
    content: "\f1f3";
}
.bxl.bx-ubuntu:before {
    content: "\f1f4";
}
.bxl.bx-udacity:before {
    content: "\f1f5";
}
.bxl.bx-unity:before {
    content: "\f1f6";
}
.bxl.bx-unsplash:before {
    content: "\f1f7";
}
.bxl.bx-upwork:before {
    content: "\f1f8";
}
.bxl.bx-v0:before {
    content: "\f1f9";
}
.bxl.bx-venmo:before {
    content: "\f1fa";
}
.bxl.bx-vercel:before {
    content: "\f1fb";
}
.bxl.bx-vimeo:before {
    content: "\f1fc";
}
.bxl.bx-visa:before {
    content: "\f1fd";
}
.bxl.bx-visual-studio:before {
    content: "\f1fe";
}
.bxl.bx-vite-js:before {
    content: "\f1ff";
}
.bxl.bx-vk:before {
    content: "\f200";
}
.bxl.bx-vuejs:before {
    content: "\f201";
}
.bxl.bx-waze:before {
    content: "\f202";
}
.bxl.bx-web-components:before {
    content: "\f203";
}
.bxl.bx-webflow:before {
    content: "\f204";
}
.bxl.bx-weibo:before {
    content: "\f205";
}
.bxl.bx-whatsapp-square:before {
    content: "\f206";
}
.bxl.bx-whatsapp:before {
    content: "\f207";
}
.bxl.bx-wikipedia:before {
    content: "\f208";
}
.bxl.bx-windsurf:before {
    content: "\f209";
}
.bxl.bx-wix:before {
    content: "\f20a";
}
.bxl.bx-wordpress:before {
    content: "\f20b";
}
.bxl.bx-work-os:before {
    content: "\f20c";
}
.bxl.bx-xai:before {
    content: "\f20d";
}
.bxl.bx-xbox:before {
    content: "\f20e";
}
.bxl.bx-xing:before {
    content: "\f20f";
}
.bxl.bx-yahoo:before {
    content: "\f210";
}
.bxl.bx-yarn:before {
    content: "\f211";
}
.bxl.bx-yelp:before {
    content: "\f212";
}
.bxl.bx-youtube-music:before {
    content: "\f213";
}
.bxl.bx-youtube:before {
    content: "\f214";
}
.bxl.bx-zen-browser:before {
    content: "\f215";
}
.bxl.bx-zoom-workplace:before {
    content: "\f216";
}
