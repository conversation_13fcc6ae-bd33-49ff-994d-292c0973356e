@extends('layouts.admin')

@section('title', 'Utilities Management - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Utilities Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Utilities</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ri-check-circle-line me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ri-error-warning-line me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        Utilities
                    </div>
                    <div class="d-flex">
                        <div class="me-2">
                            <span class="badge bg-primary-transparent">Total Utilities: {{ $utilities->total() }}</span>
                        </div>
                        <div class="ms-3">
                            <a href="{{ route('admin.utilities.create') }}" class="btn btn-primary btn-sm">
                                <i class="ri-add-line me-1"></i>Add New Utility
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">



                    @if ($utilities->count() > 0)
                        <!-- Utilities Table -->
                        <div class="table-responsive">
                            <table class="table text-nowrap table-hover utilities-table">
                                <thead>
                                    <tr>
                                        <th>Icon</th>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Rate</th>
                                        <th>Status</th>
                                        <th>Last Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                @foreach($utilities as $utility)
                                    <tr>
                                        <td>
                                            <i class="{{ $utility->icon_class ?? 'ri-tools-line' }}"
                                                style="font-size: 1.2rem;"></i>
                                        </td>
                                        <td>
                                            <div class="fw-semibold">{{ $utility->name }}</div>
                                        </td>
                                        <td class="description-cell">
                                            @if ($utility->description)
                                                <div class="description-text">
                                                    {{ $utility->description }}
                                                </div>
                                            @else
                                                <span class="text-muted">No description</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span
                                                class="fw-semibold text-success">{{ $utility->formatted_hourly_rate }}</span>
                                        </td>
                                        <td>
                                            @if ($utility->is_active ?? true)
                                                <span class="badge bg-primary-transparent">Active</span>
                                            @else
                                                <span class="badge bg-danger-transparent">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                {{ $utility->updated_at->format('M d, Y') }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.utilities.edit', $utility) }}"
                                                    class="btn btn-sm btn-warning" title="Edit Utility">
                                                    <i class="ri-edit-line"></i>
                                                </a>

                                                @if ($utility->canBeDeleted())
                                                    <button type="button" class="btn btn-sm btn-danger"
                                                        title="Delete Utility"
                                                        onclick="confirmDelete({{ $utility->id }}, '{{ $utility->name }}')">
                                                        <i class="ri-delete-bin-line"></i>
                                                    </button>
                                                @else
                                                    <button type="button" class="btn btn-sm btn-secondary"
                                                        title="Cannot delete - in use by fields" disabled>
                                                        <i class="ri-delete-bin-line"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="booking-pagination">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="pagination-info">
                                    Showing {{ $utilities->firstItem() }} to {{ $utilities->lastItem() }}
                                    of {{ $utilities->total() }} utilities
                                </div>
                                <div class="admin-pagination">
                                    <x-pagination :paginator="$utilities" />
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="d-flex flex-column align-items-center">
                                <i class="ri-tools-line" style="font-size: 3rem; color: #6c757d;"></i>
                                <h6 class="mt-2 mb-1">No Utilities Found</h6>
                                <p class="text-muted mb-3">Start by creating your first utility to enhance
                                    field functionality.</p>
                                <a href="{{ route('admin.utilities.create') }}" class="btn btn-primary">
                                    <i class="ri-add-line me-1"></i>Create First Utility
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <x-confirmation-modal
        modal-id="deleteModal"
        title="Are you sure you want to delete the utility &quot;<span id='utilityName' class='fw-semibold'></span>&quot;?"
        cancel-text="No, Cancel"
        confirm-text="Yes, Delete"
        target-element-id="utilityName"
    />
@endsection

@push('styles')
<style>
    /* Utilities table description column styling */
    .utilities-table .description-cell {
        white-space: normal !important;
        word-wrap: break-word;
        word-break: break-word;
        max-width: 300px;
        min-width: 200px;
        vertical-align: top;
    }

    .utilities-table .description-text {
        line-height: 1.4;
        padding: 0;
        margin: 0;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    /* Ensure other columns maintain their nowrap behavior */
    .utilities-table td:not(.description-cell) {
        white-space: nowrap;
    }



    /* Responsive adjustments */
    @media (max-width: 768px) {
        .utilities-table .description-cell {
            max-width: 200px;
            min-width: 150px;
        }
    }

    @media (max-width: 576px) {
        .utilities-table .description-cell {
            max-width: 150px;
            min-width: 120px;
        }
    }
</style>
@endpush

@push('scripts')
    <script>
        // Delete confirmation function
        function confirmDelete(utilityId, utilityName) {
            document.getElementById('utilityName').textContent = utilityName;
            document.getElementById('deleteModalForm').action = `/admin/utilities/${utilityId}`;
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
@endpush
